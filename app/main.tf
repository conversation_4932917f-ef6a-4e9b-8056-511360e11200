terraform {
  backend "gcs" {}

  required_providers {
    archive = {
      source  = "hashicorp/archive"
      version = "1.3.0"
    }
    external = {
      source  = "hashicorp/external"
      version = "~> 2.0"
    }
    google = {
      source  = "hashicorp/google"
      version = ">= 5.19.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "2.18.1"
    }
    null = {
      source  = "hashicorp/null"
      version = "~> 3.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 2.2"
    }
    vault = {
      source = "hashicorp/vault"
    }
  }
}

module "join_gke_autopilot" {
  source = "git::https://gitlab.gcp.davita.com/p360/gke.git?ref=v3.4.4"
  cloud  = "gcp"
  region = "us-central1"
  env    = "${var.env}_autopilot"
}

locals {
  endpoint        = substr(lower(sha256(replace(var.CI_COMMIT_REF_NAME, "/[^a-z0-9_]+/", "-"))), 0, 5)
  frontend_domain = terraform.workspace == "default" ? "${var.env == "p" ? "messaging-micro-frontend.patient.davita.com" : "frontend-messaging-micro-frontend.patient-np.davita.com"}" : "${var.env == "p" ? "frontend${local.endpoint}-messaging-micro-frontend.patient.davita.com" : "frontend-${local.endpoint}-messaging-micro-frontend.patient-np.davita.com"}"
  display         = var.env == "p" ? "Prod" : "Non-Prod"
  token           = data.google_client_config.default.access_token
  project_id      = module.join_gke_autopilot.env.project_id
}


provider "kubernetes" {
  alias                  = "k8s"
  host                   = "https://${module.join_gke_autopilot.env.cluster_host}"
  token                  = local.token
  cluster_ca_certificate = base64decode(module.join_gke_autopilot.env.cluster_ca_cert)
}

provider "helm" {
  helm_driver = "configmap"
  alias       = "helm"

  kubernetes {
    host                   = "https://${module.join_gke_autopilot.env.cluster_host}"
    token                  = local.token
    cluster_ca_certificate = base64decode(module.join_gke_autopilot.env.cluster_ca_cert)
  }
}

provider "vault" {
  namespace = "p360"
  alias     = "p360"
}

data "google_client_config" "default" {}

module "k8s" {
  providers = {
    kubernetes.k8s = kubernetes.k8s
    helm.helm      = helm.helm
    vault.p360     = vault.p360
  }

  count                 = var.GCSPROXY_DEPLOYMENT == "gcsproxy" ? 0 : 1
  source                = "./modules/k8s"
  env                   = var.env
  endpoint              = local.endpoint
  namespace             = var.namespace
  CI_PROJECT_NAME       = var.CI_PROJECT_NAME
  display               = local.display
  frontend_domain       = local.frontend_domain
  frontend_image_url    = var.frontend_image_url
  frontend_image_digest = var.frontend_image_digest
  DATABASE_ENVIRONMENT  = var.DATABASE_ENVIRONMENT
  token                 = local.token
}

module "monitoring" {
  source          = "./modules/monitoring"
  count           = var.CREATE_ALERTS == true ? 1 : 0
  env             = var.env
  endpoint        = local.endpoint
  project_id      = local.project_id
  frontend_domain = local.frontend_domain
}

module "gcsproxy" {
  count           = var.GCSPROXY_DEPLOYMENT == "gcsproxy" ? 1 : 0
  source          = "./modules/gcsproxy"
  env             = var.env
  endpoint        = local.endpoint
  frontend_domain = local.frontend_domain
}

