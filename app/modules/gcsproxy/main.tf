module "join_gke_autopilot" {
  source = "git::https://gitlab.gcp.davita.com/p360/gke.git"
  cloud  = "gcp"
  region = "us-central1"
  env    = "${var.env}_autopilot"
}

module "join_p360_program_infra" {
  source    = "git::https://gitlab.gcp.davita.com/p360/p360-program.git"
  cloud     = "gcp"
  region    = "us-central1"
  workspace = terraform.workspace
  env       = var.env
}

locals {
  project_id                   = module.join_gke_autopilot.env.project_id
  tf_remote_project            = module.join_p360_program_infra.env.tf_remote_state_project_id
  backend_service_nginx_status = var.env == "p" ? "k8s-be-30080--b27117789c5f2ec7" : "k8s-be-30080--b7046a5ae1a7fe91"
  instance_group               = var.env == "p" ? "k8s-ig--b27117789c5f2ec7" : "k8s-ig--b7046a5ae1a7fe91"
}

data "google_compute_backend_service" "nginx_status" {
  name    = local.backend_service_nginx_status
  project = local.project_id
}

data "google_compute_instance_group" "instance_a" {
  name    = local.instance_group
  zone    = "us-central1-a"
  project = local.project_id
}
data "google_compute_instance_group" "instance_b" {
  name    = local.instance_group
  zone    = "us-central1-b"
  project = local.project_id
}
data "google_compute_instance_group" "instance_c" {
  name    = local.instance_group
  zone    = "us-central1-c"
  project = local.project_id
}
data "google_compute_instance_group" "instance_f" {
  name    = local.instance_group
  zone    = "us-central1-f"
  project = local.project_id
}

module "gce-lb-https" {
  source                          = "terraform-google-modules/lb-http/google"
  version                         = "~> 10.1.0"
  project                         = local.project_id
  name                            = "messaging-mfe-be-${var.endpoint}"
  ssl                             = true
  ssl_policy                      = "ssl-policy"
  managed_ssl_certificate_domains = [var.frontend_domain]
  firewall_networks               = ["gke"]
  // Make sure when you create the cluster that you provide the `--tags` argument to add the appropriate `target_tags` referenced in the http module.
  # target_tags = [var.target_tags]

  url_map        = google_compute_url_map.th_url_map.self_link
  create_url_map = false
  backends = {
    "be-nginx-status" = {
      protocol                        = data.google_compute_backend_service.nginx_status.protocol
      port                            = tonumber(trimprefix(data.google_compute_backend_service.nginx_status.port_name, "port"))
      port_name                       = data.google_compute_backend_service.nginx_status.port_name
      timeout_sec                     = data.google_compute_backend_service.nginx_status.timeout_sec
      enable_cdn                      = data.google_compute_backend_service.nginx_status.enable_cdn
      connection_draining_timeout_sec = data.google_compute_backend_service.nginx_status.connection_draining_timeout_sec
      health_check = {
        request_path = "/nginx_status"
        port_name    = data.google_compute_backend_service.nginx_status.port_name
        port         = tonumber(trimprefix(data.google_compute_backend_service.nginx_status.port_name, "port"))
        logging      = true
      }

      log_config = {
        enable      = true
        sample_rate = 1
      }
      groups = [
        {
          group                 = data.google_compute_instance_group.instance_a.self_link
          balancing_mode        = "RATE"
          max_rate_per_instance = 1
        },
        {
          group                 = data.google_compute_instance_group.instance_b.self_link
          balancing_mode        = "RATE"
          max_rate_per_instance = 1
        },
        {
          group                 = data.google_compute_instance_group.instance_c.self_link
          balancing_mode        = "RATE"
          max_rate_per_instance = 1
        },
        {
          group                 = data.google_compute_instance_group.instance_f.self_link
          balancing_mode        = "RATE"
          max_rate_per_instance = 1
        },
      ]
      iap_config = {
        enable = false
      }
    }
  }
}


resource "google_compute_url_map" "th_url_map" {
  project = local.project_id
  // Load Blancer name
  name            = "messaging-mfe-lb-${var.endpoint}"
  provider        = google-beta
  default_service = module.gce-lb-https.backend_services["be-nginx-status"].self_link
  host_rule {
    hosts        = [var.frontend_domain]
    path_matcher = "allpaths"
  }
  path_matcher {
    name            = "allpaths"
    default_service = module.gce-lb-https.backend_services["be-nginx-status"].self_link
    path_rule {
      paths = [
        "/*"
      ]
      service = module.gce-lb-https.backend_services["be-nginx-status"].self_link
    }
  }
}

resource "google_dns_record_set" "frontend_a" {
  project      = local.tf_remote_project
  name         = join("", [var.frontend_domain, "."])
  managed_zone = "patient-dns-zone"
  type         = "A"
  ttl          = 600
  rrdatas      = [module.gce-lb-https.external_ip]
}
