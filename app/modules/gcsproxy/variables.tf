variable "env" {
  type        = string
  description = "target environment for deployment"
}

variable "endpoint" {
  type = string
}

variable "frontend_domain" {
  type = string
}

variable "resource_labels" {
  type        = map(string)
  description = "the map of labels which must be associated with every resource that supports labels"
  default     = {}
}


variable "CI_COMMIT_REF_NAME" {
  type        = string
  description = "Gitlab CI commit ref name (i.e. branch)"
  default     = ""
}

variable "CI_PROJECT_NAME" {
  type        = string
  description = "Gitlab CI project name"
  default     = ""
}

variable "CI_PROJECT_TITLE" {
  type        = string
  description = "Gitlab CI project title"
  default     = ""
}

variable "CI_PROJECT_PATH" {
  type        = string
  description = "Gitlab CI project path"
  default     = ""
}

variable "CI_COMMIT_SHORT_SHA" {
  type        = string
  description = "Gitlab CI commit short SHA"
  default     = ""
}

variable "CI_JOB_ID" {
  type        = string
  description = "Gitlab CI job ID"
  default     = ""
}

variable "CI_PIPELINE_ID" {
  type        = string
  description = "Gitlab CI Pipeline ID"
  default     = ""
}

# <PERSON>'s code starts here
variable "region" {
  type        = string
  description = "The region of the Cloud SQL resources"
  default     = "us-central1"
}

variable "zone" {
  type        = string
  description = "The zone for the master instance."
  default     = "us-central1-a"
}


variable "DATABASE_ENVIRONMENT" {
  type        = string
  description = "Database Environment driven by labels"
  default     = "dev"
}
