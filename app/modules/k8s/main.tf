terraform {
  required_providers {
    kubernetes = {
      source = "hashicorp/kubernetes"
      configuration_aliases = [
        kubernetes.k8s
      ]
    }
    helm = {
      source = "hashicorp/helm"
      configuration_aliases = [
        helm.helm
      ]
    }
    vault = {
      source = "hashicorp/vault"
      configuration_aliases = [
        vault.p360
      ]
    }
  }
}

module "join_gke_autopilot" {
  source = "git::https://gitlab.gcp.davita.com/p360/gke.git"
  cloud  = "gcp"
  region = "us-central1"
  env    = "${var.env}_autopilot"
}

module "join_p360_program_infra" {
  source    = "git::https://gitlab.gcp.davita.com/p360/p360-program.git"
  cloud     = "gcp"
  region    = "us-central1"
  workspace = terraform.workspace
  env       = var.env
}


locals {
  mandatory_labels = merge(var.resource_labels,
    {
      project_name = replace(lower(var.CI_PROJECT_NAME), "/[^a-z0-9_]+/", "-")
      env          = lower(var.display)
      env_name     = substr(terraform.workspace, 0, 62)
      env_type     = terraform.workspace == "default" ? lower(var.display) : "review"
    }
  )
}


resource "google_project_service" "apis" {
  for_each = toset([
    "youtube.googleapis.com",
  ])
  project                    = module.join_gke_autopilot.env.project_id
  service                    = each.value
  disable_dependent_services = false
  disable_on_destroy         = false
}

module "messaging_micro_frontend" {
  source = "./messaging-micro-frontend"
  providers = {
    helm       = helm.helm
    kubernetes = kubernetes.k8s
    vault      = vault.p360
  }

  mandatory_labels                    = local.mandatory_labels
  env                                 = var.env
  frontend_domain                     = var.frontend_domain
  billing_account                     = module.join_p360_program_infra.env.billing_account
  folder_id                           = module.join_p360_program_infra.env.folder_id
  org_id                              = module.join_p360_program_infra.env.org_id
  project_id                          = module.join_gke_autopilot.env.project_id
  project_number                      = module.join_gke_autopilot.env.project_number
  gke_project_id                      = module.join_gke_autopilot.env.project_id
  gsm_project_id                      = module.join_p360_program_infra.env.gsm_project_id
  tf_remote_project                   = module.join_p360_program_infra.env.tf_remote_state_project_id
  cluster_name                        = module.join_gke_autopilot.env.cluster_name
  cluster_host                        = module.join_gke_autopilot.env.cluster_host
  cluster_ca_cert                     = module.join_gke_autopilot.env.cluster_ca_cert
  cluster_network                     = module.join_gke_autopilot.env.network_self_link
  domain_name                         = module.join_p360_program_infra.env.domain_name
  gcp_backups_project_id              = module.join_p360_program_infra.env.gcp_backups_project_id
  access_token                        = var.token
  endpoint                            = var.endpoint
  namespace                           = var.namespace
  frontend_image_url                  = var.frontend_image_url
  frontend_image_digest               = var.frontend_image_digest
  messaging_micro_frontend_project_id = module.messaging_micro_frontend.messaging_micro_frontend_app_project_id
}

