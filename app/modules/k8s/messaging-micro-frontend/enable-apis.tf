resource "google_project_service" "apis_cloudbuild" {
  project                    = google_project.messaging_micro_frontend_app_project.project_id
  service                    = "cloudbuild.googleapis.com"
  disable_dependent_services = false
  disable_on_destroy         = false
}

resource "google_project_service" "apis_binauth" {
  project                    = google_project.messaging_micro_frontend_app_project.project_id
  service                    = "binaryauthorization.googleapis.com"
  disable_dependent_services = false
  disable_on_destroy         = false
}
