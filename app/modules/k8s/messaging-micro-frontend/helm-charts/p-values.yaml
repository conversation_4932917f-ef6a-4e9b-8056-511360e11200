deployment:
  type: split
  ng14image: ""
image:
  repository: us.gcr.io/p360-np-shared-assets-4838/messaging-micro-frontend-frontend
  pullPolicy: IfNotPresent
  frontendreplicas: 1
  project: set-by-terraform

network:
  frontenddomain: ""
  frontendipname: ""
  backendipname: ""
  gatewaydomain: ""

resources:
  limits:
    cpu: 1
    memory: 1024Mi
  requests:
    cpu: 1
    memory: 1024Mi

hpa:
  minReplicas: 2
  maxReplicas: 4
  metrics:
    memory: 60
    cpu: 60
    # socketsConnected: 100

PodDisruptionBudget:
  minAvailable: 1      

security:
  dnswhitelist:
    - matchName: 'sso.davita.com'
    - matchName: 'googleapis.com'
    - matchPattern: '*.googleapis.com'

  ipwhitelist:
    - '10.0.0.0/8'
    - '***************/32'
    - '***************/32'

#ssl-policy config
sslPolicy: "ssl-policy"