apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: messaging-micro-frontend
    tier: frontend
  name: messaging-micro-frontend
spec:
  replicas: {{ .Values.image.frontendreplicas }}
  selector:
    matchLabels:
      app: messaging-micro-frontend
      tier: frontend
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: messaging-micro-frontend
        tier: frontend
    spec:
      serviceAccountName: messagingmicrofrontendsa
      containers:
        - name: messaging-micro-frontend
          image: "{{ .Values.image.repository }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: ENDPOINT
              value: {{ .Values.environment.endpoint | quote }}
            - name: ENV
              value: {{ .Chart.AppVersion }}
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /
              port: http
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
