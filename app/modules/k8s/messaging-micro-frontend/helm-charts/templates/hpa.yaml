apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: messaging-micro-frontend-scaler
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: messaging-micro-frontend
  minReplicas: {{ .Values.hpa.minReplicas }}
  maxReplicas: {{ .Values.hpa.maxReplicas }}
  metrics:
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.hpa.metrics.memory }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.hpa.metrics.cpu }}
# https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale-walkthrough/
# - type: Pods
#   pods:
#     metric:
#       name: socketsConnected
#     target:
#       type: AverageValue
#       averageValue: {{ .Values.hpa.metrics.socketsConnected }}
