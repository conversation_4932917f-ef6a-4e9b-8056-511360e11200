---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: frontendcert
spec:
  domains:
    - {{ .Values.network.frontenddomain }}
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name:  messaging-micro-frontendingress
  annotations:
    kubernetes.io/ingress.global-static-ip-name: {{ .Values.network.frontendipname }}
    networking.gke.io/managed-certificates: frontendcert
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  rules:
  - host: {{ .Values.network.frontenddomain }}
    http:
      paths:
      - pathType: ImplementationSpecific
        path: /*
        backend:
          service:
            name: messaging-micro-frontend-service
            port:
              number: 80
