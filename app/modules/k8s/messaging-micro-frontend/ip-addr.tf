resource "google_compute_global_address" "frontend_ip" {
  name         = "messagingmicrodavitafrontend-ip-${random_string.hex.result}"
  address_type = "EXTERNAL"
  project      = var.gke_project_id
}

resource "random_string" "hex" {
  length    = 4
  min_lower = 4
  number    = false
  special   = false
}

resource "google_dns_record_set" "frontend" {
  project      = var.tf_remote_project
  name         = join("", [local.frontend_domain, "."])
  managed_zone = "patient-dns-zone"
  type         = "A"
  ttl          = 600
  rrdatas      = [google_compute_global_address.frontend_ip.address]
}
