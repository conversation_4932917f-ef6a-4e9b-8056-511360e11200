resource "random_string" "suffix" {
  length    = 4
  min_lower = 4
  number    = false
  special   = false
}

#being set but not used. Ready for when it is necessary
#data "vault_generic_secret" "secret" {
#  path = var.vault_secret_path
#}

resource "google_project" "messaging_micro_frontend_app_project" {
  # needed for firebase
  provider = google-beta

  name            = "messaging-micro-fe-${var.env == "p" ? "p" : "np"}"
  project_id      = "messaging-micro-fe-${var.env == "p" ? "p" : "np"}-${random_string.suffix.result}"
  folder_id       = var.folder_id
  billing_account = var.billing_account

  auto_create_network = false

  labels = var.mandatory_labels
}
