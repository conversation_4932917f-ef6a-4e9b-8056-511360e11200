
# This uses the STAGE deployment for the nonprod vars but the DEV containers
# we specify the secrets here instead of the vars file in anticipation of using data.vault.something.something...
# This basically needs a complete vault rewrite when that comes out.
locals {
  frontend_domain = var.frontend_domain
  frontend_ipname = google_compute_global_address.frontend_ip.name
  #cloudsqlproxyhost = "${var.gke_project_id}:${var.region}:${var.cloudsql_instance_name}=tcp:127.0.0.1:5432"
}

resource "helm_release" "messaging_micro_frontend" {
  name             = "messaging-micro-frontend-dev-${random_string.suffix.result}"
  chart            = "./${path.module}/helm-charts"
  namespace        = var.namespace
  create_namespace = true
  values = [
    "${file("./${path.module}/helm-charts/${var.env}-values.yaml")}"
  ]

  set {
    name  = "environment.endpoint"
    value = var.endpoint
  }

  set {
    name  = "image.frontendreplicas"
    value = "1"
  }

  set {
    name  = "environment.refreshit"
    value = timestamp()
  }
  set {
    name = "network.cors"
    value = join("\\,", [
      "https://${local.frontend_domain}",
    ])
  }
  set {
    name  = "image.project"
    value = var.gke_project_id
  }

  set {
    name  = "image.repository"
    value = var.frontend_image_url
  }
  set {
    name  = "network.frontenddomain"
    value = local.frontend_domain
  }
  set {
    name  = "network.frontendipname"
    value = local.frontend_ipname
  }
  set {
    name  = "security.ipwhitelist[0]"
    value = "**************/32"
  }
  set {
    name  = "security.ipwhitelist[1]"
    value = "***************/32"
  }
  set {
    name  = "security.ipwhitelist[2]"
    value = "10.0.0.0/8"
  }
  set {
    name  = "security.ipwhitelist[3]"
    value = "************/24"
  }
  set {
    name  = "security.ipwhitelist[4]"
    value = "**********/16"
  }
  set {
    name  = "security.ipwhitelist[5]"
    value = "***********/22"
  }
  set {
    name  = "security.ipwhitelist[6]"
    value = "127.0.0.1/32"
  }

  set {
    name  = "security.ipwhitelist[7]"
    value = "http://localhost:8080"
  }

}
