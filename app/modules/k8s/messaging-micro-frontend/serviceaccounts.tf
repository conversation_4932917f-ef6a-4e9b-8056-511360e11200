resource "kubernetes_namespace" "k8_namespace" {
  metadata {
    name = var.namespace
  }
}

resource "kubernetes_labels" "k8namespace_additional_labels" {
  api_version = "v1"
  kind        = "Namespace"
  metadata {
    name = var.namespace
  }
  labels = {
    "pod-security.kubernetes.io/enforce" = "baseline"
  }
  depends_on = [
    kubernetes_namespace.k8_namespace
  ]
}

resource "kubernetes_secret_v1" "k8_sa_secret" {
  metadata {
    name      = "messagingmicrofrontendsa-token-${random_string.suffix.result}"
    namespace = var.namespace
    annotations = {
      "kubernetes.io/service-account.name" = kubernetes_service_account.messagingmicrofrontendpatientplatformfrontendsa.metadata.0.name
    }
  }

  type = "kubernetes.io/service-account-token"
  depends_on = [
    kubernetes_service_account.messagingmicrofrontendpatientplatformfrontendsa
  ]
}

resource "kubernetes_service_account" "messagingmicrofrontendpatientplatformfrontendsa" {
  metadata {
    name      = "messagingmicrofrontendsa"
    namespace = var.namespace
    annotations = {
      "iam.gke.io/gcp-service-account" = "messagingmicrofrontendsa-${random_string.suffix.result}@${var.messaging_micro_frontend_project_id}.iam.gserviceaccount.com"
    }
  }
}


resource "google_service_account" "messagingmicrofrontendpatientplatformfrontendsa" {
  account_id = "messagingmicrofrontendsa-${random_string.suffix.result}"
  project    = var.messaging_micro_frontend_project_id
}

resource "google_service_account_iam_member" "frontend_workload_id" {
  service_account_id = google_service_account.messagingmicrofrontendpatientplatformfrontendsa.name
  role               = "roles/iam.workloadIdentityUser"
  member             = "serviceAccount:${var.gke_project_id}.svc.id.goog[${var.namespace}/messagingmicrofrontendsa]"
  depends_on         = [kubernetes_namespace.k8_namespace]
}

