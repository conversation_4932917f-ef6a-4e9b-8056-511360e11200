variable "tf_remote_project" {}
variable "cluster_name" {}
variable "cluster_host" {}
variable "cluster_ca_cert" {}
variable "cluster_network" {}
variable "domain_name" {}
variable "gcp_backups_project_id" {}

variable "env" {
  type        = string
  description = "p|np"
  default     = "np"
}

variable "billing_account" {
  type        = string
  description = "Billing account"
}

variable "folder_id" {
  type        = string
  description = "Folder id"
}

variable "org_id" {
  type        = string
  description = "organization id"
}

variable "mandatory_labels" {
  type        = map(any)
  description = "map of labels to add to all resources which support them"
  default     = {}
}




# new vars from moving resources into /program

variable "region" {
  type        = string
  description = "The region of the Cloud SQL resources"
  default     = "us-central1"
}

variable "zone" {
  type        = string
  description = "The zone for the master instance."
  default     = "us-central1-a"
}

variable "project_id" {
  type = string
}

variable "project_number" {
  type = string
}

variable "gke_project_id" {
  type = string
}

variable "gsm_project_id" {
  type = string
}

variable "namespace" {
  default = "messaging_micro_frontend"
}

variable "access_token" {}
variable "frontend_image_url" {}
variable "frontend_image_digest" {}


# variable "vault_secret_path" {}
variable "endpoint" {
  type = string
}

variable "messaging_micro_frontend_project_id" {
  type = string
}

variable "frontend_domain" {}
