variable "resource_labels" {
  type        = map(string)
  description = "the map of labels which must be associated with every resource that supports labels"
  default     = {}
}

variable "env" {
  type        = string
  description = "target environment for deployment"
}

variable "namespace" {
  default = "messaging_micro_fe"
}

variable "frontend_image_url" {
  type        = string
  description = "URL for loading of frontend container image"
}

variable "frontend_image_digest" {
}
variable "frontend_domain" {}

variable "DATABASE_ENVIRONMENT" {

}

variable "CI_PROJECT_NAME" {
  type = string
}


variable "token" {
  type = string
}

variable "endpoint" {
  type = string
}

variable "display" {
  type = string
}
