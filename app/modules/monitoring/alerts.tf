
resource "google_monitoring_alert_policy" "uptime_check_alert" {
  project               = var.project_id
  display_name          = "${var.env == "p" ? "Production" : "Non-Prod"}-messaging-mfe-uptime-check-${var.endpoint}"
  combiner              = "OR"
  notification_channels = [google_monitoring_notification_channel.email.id]

  conditions {
    display_name = "Uptime Check Condition"
    condition_threshold {
      filter     = "metric.type=\"monitoring.googleapis.com/uptime_check/check_passed\" AND resource.type=\"uptime_url\" AND resource.labels.host=\"${var.frontend_domain}\""
      comparison = "COMPARISON_LT"
      aggregations {
        alignment_period = "60s"
      }
      threshold_value = 1
      duration        = "300s"
    }
  }

  documentation {
    content   = "The website is down or not responding correctly."
    mime_type = "text/markdown"
  }

  user_labels = {
    alert_type = "uptime_check"
  }
}
