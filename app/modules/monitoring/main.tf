
# uptime monitoring
resource "google_monitoring_notification_channel" "email" {
  project      = var.project_id
  display_name = "Email Notification Channel"
  type         = "email"
  labels = {
    email_address = "<EMAIL>"
  }
}

# resource "google_monitoring_notification_channel" "teams" {
#   project      = var.project_id
#   display_name = "Teams Notification Channel"
#   type         = "webhook_tokenauth"
#   labels = {
#     auth_token = "YOUR_TEAMS_WEBHOOK_AUTH_TOKEN"
#     url        = "https://outlook.office.com/webhook/YOUR_WEBHOOK_URL"
#   }
#   user_labels = {
#     channel = "Teams"
#   }
# }
