resource "google_monitoring_uptime_check_config" "uptime_check" {
  project      = var.project_id
  display_name = "${var.env == "p" ? "Production" : "Non-Prod"}-messaging-mfe-uptime-check-${var.endpoint}"
  timeout      = "10s"
  period       = "60s"

  monitored_resource {
    type = "uptime_url"
    labels = {
      project_id = var.project_id
      host       = var.frontend_domain
    }
  }

  http_check {
    path           = "/"
    port           = 443
    request_method = "GET"
    use_ssl        = true
  }

  selected_regions = [
    "usa",
  ]

  content_matchers {
    content = "<title>messaging-micro-frontend</title>"
    matcher = "CONTAINS_STRING"
  }
}
