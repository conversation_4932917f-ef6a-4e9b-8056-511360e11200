variable "env" {
  type        = string
  description = "target environment for deployment"
}

variable "CI_COMMIT_REF_NAME" {
  type        = string
  description = "Gitlab CI commit ref name (i.e. branch)"
  default     = ""
}

variable "CI_PROJECT_NAME" {
  type        = string
  description = "Gitlab CI project name"
  default     = ""
}

variable "CREATE_ALERTS" {
  type        = bool
  description = "flag to create alerts"
  default     = true
}

variable "CI_PROJECT_TITLE" {
  type        = string
  description = "Gitlab CI project title"
  default     = ""
}

variable "CI_PROJECT_PATH" {
  type        = string
  description = "Gitlab CI project path"
  default     = ""
}

variable "CI_COMMIT_SHORT_SHA" {
  type        = string
  description = "Gitlab CI commit short SHA"
  default     = ""
}

variable "CI_JOB_ID" {
  type        = string
  description = "Gitlab CI job ID"
  default     = ""
}

variable "CI_PIPELINE_ID" {
  type        = string
  description = "Gitlab CI Pipeline ID"
  default     = ""
}

# <PERSON>'s code starts here
variable "region" {
  type        = string
  description = "The region of the Cloud SQL resources"
  default     = "us-central1"
}

variable "zone" {
  type        = string
  description = "The zone for the master instance."
  default     = "us-central1-a"
}

variable "namespace" {
  default = "unified_patient_platform"
}
variable "frontend_image_url" {
  type        = string
  description = "URL for loading of frontend container image"
}

variable "frontend_image_digest" {

}

variable "VAULT_SECRET_PATH_PREFIX" {
  description = "Path for the vault engine inside the namespace"
}

variable "DATABASE_ENVIRONMENT" {
  type        = string
  description = "Database Environment driven by labels"
  default     = "dev"
}

variable "GCSPROXY_DEPLOYMENT" {
  type    = string
  default = "gcsproxy"
}
