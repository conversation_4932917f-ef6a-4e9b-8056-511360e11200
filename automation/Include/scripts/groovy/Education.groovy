//package ckd_education
import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject

import com.kms.katalon.core.annotation.Keyword
import com.kms.katalon.core.checkpoint.Checkpoint
import com.kms.katalon.core.checkpoint.CheckpointFactory
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling
import com.kms.katalon.core.testcase.TestCase
import com.kms.katalon.core.testcase.TestCaseFactory
import com.kms.katalon.core.testdata.TestData
import com.kms.katalon.core.testdata.TestDataFactory
import com.kms.katalon.core.testobject.ObjectRepository
import com.kms.katalon.core.testobject.TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI

import internal.GlobalVariable

import org.openqa.selenium.WebElement
import org.openqa.selenium.WebDriver
import org.openqa.selenium.By

import com.kms.katalon.core.mobile.keyword.internal.MobileDriverFactory
import com.kms.katalon.core.webui.driver.DriverFactory

import com.kms.katalon.core.testobject.RequestObject
import com.kms.katalon.core.testobject.ResponseObject
import com.kms.katalon.core.testobject.ConditionType
import com.kms.katalon.core.testobject.TestObjectProperty

import com.kms.katalon.core.mobile.helper.MobileElementCommonHelper
import com.kms.katalon.core.util.KeywordUtil

import com.kms.katalon.core.webui.exception.WebElementNotFoundException

import cucumber.api.java.en.And
import cucumber.api.java.en.Given
import cucumber.api.java.en.Then
import cucumber.api.java.en.When


class Education {

	@Given("that an Authenticated Pateint is on CKD Homepage")
	def Home_Page() {

		WebUI.openBrowser('')

		WebUI.maximizeWindow()

		WebUI.navigateToUrl('https://ckd-04bdd.patient-np.davita.com')



		WebUI.setText(findTestObject('Object Repository/PIAM/input_Sign in or create an account_mat-input-0'), 'DHAPATEL')

		WebUI.click(findTestObject('Object Repository/PIAM/div_Password'))

		WebUI.setEncryptedText(findTestObject('Object Repository/PIAM/input_Username or Email_mat-input-1'), 'wam7MQ9/3PCV54S2HpRUxA==')

		WebUI.click(findTestObject('Object Repository/PIAM/button_LOGIN'))

		WebUI.waitForElementPresent(findTestObject('Page_DaVita-CKD/div_Home'), 0)

		WebUI.verifyElementPresent(findTestObject('Page_DaVita-CKD/div_Home'),0 )
	}

	@When("Clicks on Education")
	def Education_page() {

		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_DaVita-CKD/div_Education'), 0)

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/div_Education'))
	}

	@Then("Validate the Patient can see and open all the education categories and title of the category should be visible on the page")
	def Education_Categories() {

		WebUI.click(findTestObject('Object Repository/CKD Education/Eduacation catagories Taking Control of Kidney Disease'))

		WebUI.click(findTestObject('Object Repository/CKD Education/span_BACK TO EDUCATION'))

		WebUI.click(findTestObject('Object Repository/CKD Education/Education Categories All About Kidney Transplant'))

		WebUI.click(findTestObject('Object Repository/CKD Education/span_BACK TO EDUCATION'))

		WebUI.click(findTestObject('Object Repository/CKD Education/Education Categories Home Treatment Options'))

		WebUI.click(findTestObject('Object Repository/CKD Education/span_BACK TO EDUCATION'))

		WebUI.click(findTestObject('Object Repository/CKD Education/Education Categories Other Health Tools'))

		WebUI.click(findTestObject('Object Repository/CKD Education/span_BACK TO EDUCATION'))

		WebUI.click(findTestObject('Object Repository/CKD Education/Education Categories Treatment Choices and Preparation'))

		WebUI.click(findTestObject('Object Repository/CKD Education/span_BACK TO EDUCATION'))

		WebUI.click(findTestObject('Object Repository/CKD Education/Education Categories Working and Insurance'))

		WebUI.click(findTestObject('Object Repository/CKD Education/span_BACK TO EDUCATION'))

		WebUI.click(findTestObject('Object Repository/CKD Education/Education Categories Kidney-Friendly Diet and Nutrition'))

		WebUI.click(findTestObject('Object Repository/CKD Education/span_BACK TO EDUCATION'))

		WebUI.click(findTestObject('Object Repository/CKD Education/Education Categories Educación en Español'))

		WebUI.click(findTestObject('Object Repository/CKD Education/span_BACK TO ENGLISH EDUCATION'))
	}

	@And("Validate the user can see the titles and images of each card")
	def Title_and_Images() {

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Eduacation catagories Taking Control of Kidney Disease'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Education Categories Kidney-Friendly Diet and Nutrition'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Education Categories All About Kidney Transplant'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Education Categories Educación en Español'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Education Categories Home Treatment Options'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Education Categories Other Health Tools'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Education Categories Treatment Choices and Preparation'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Education Categories Working and Insurance'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_DaVita-CKD/Title Image Taking Control of Kidney Disease'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Title Image Educación en Español'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Title Image Home Treatment Options'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Title Image Kidney-Friendly Diet and Nutrition'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Title Image Other Health Tools'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Title Image Taking Control of Kidney Disease'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Title Image Treatment Choices and Preparation'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/Title Image Working and Insurance'), 0)
	}

	@Given("An Authenticated patient is on CKD Education page")
	def ckd_education_page() {

		WebUI.openBrowser('')

		WebUI.navigateToUrl('https://ckd-04bdd.patient-np.davita.com')

		WebUI.setText(findTestObject('Object Repository/PIAM/input_Sign in or create an account_mat-input-0'), 'DHAPATEL')

		WebUI.click(findTestObject('Object Repository/PIAM/div_Password'))

		WebUI.setEncryptedText(findTestObject('Object Repository/PIAM/input_Username or Email_mat-input-1'), 'wam7MQ9/3PCV54S2HpRUxA==')

		WebUI.click(findTestObject('Object Repository/PIAM/button_LOGIN'))

		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_DaVita-CKD/div_Education'), 0)

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/div_Education'))
	}

	@When("Clicks on any Education Categories")
	def ckd_education_categorie() {

		WebUI.click(findTestObject('Object Repository/CKD Education/Eduacation catagories Taking Control of Kidney Disease'))
	}

	@And("after opening a document clicks on continue button")
	def education_document() {

		WebUI.click(findTestObject('Object Repository/CKD Education/div_What do Kidneys Do'))

		WebUI.waitForElementPresent(findTestObject('Object Repository/CKD Education/button_CONTINUE'), 0)

		WebUI.click(findTestObject('Object Repository/CKD Education/button_CONTINUE'))
	}

	@Then("Patient should be redirected to categories selected previously")
	def document_continue() {

		WebUI.waitForElementPresent(findTestObject('Object Repository/CKD Education/div_Taking Control of Kidney Disease'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Education/div_Taking Control of Kidney Disease'), 0)
	}

	@And("Open a video")
	def video() {

		WebUI.click(findTestObject('Object Repository/CKD Education/div_How Kidneys Work'))
	}


	@And("after opening a video click on back button")
	def video_back() {

		WebUI.click(findTestObject('Object Repository/CKD Education/div_How Kidneys Work'))

		WebUI.click(findTestObject('Object Repository/CKD Education/span_BACK TO TAKING CONTROL OF KIDNEY DISEASE'))
	}

	@Then("user should be able to play and pause the videos")
	def video_play_pause() {

		WebUI.click(findTestObject('Object Repository/CKD Education/mat-icon_play_arrow'))

		WebUI.click(findTestObject('Object Repository/CKD Education/mat-icon_pause'))

		WebUI.click(findTestObject('Object Repository/CKD Education/mat-icon_play_arrow'))

		WebUI.click(findTestObject('Object Repository/CKD Education/mat-icon_pause'))
	}

	@Then("Validate that on Education page Footer links:- Terms Of Use, Privacy Policy, Medical Advice Disclaimer, Accessibility Statement are working fine")
	def footers_links() {

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/p_Terms of Use'))

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/button_Close'))

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/p_Privacy Policy'))

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/button_Close'))

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/p_Medical Advice Disclaimer'))

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/button_Close'))

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/p_Accessibility Statement'))

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/button_Close'))
	}

	@Then("Validate that Patient is able to see the Green check on the top right corner of the card for all completed cards")
	def completed_cards() {

		if (WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/Causes of Kidney Disease 100'), 0, FailureHandling.OPTIONAL) ) {

			WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/Causes of Kidney Disease green checkmark'), 0)
		} else {

			WebUI.verifyElementNotPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/Causes of Kidney Disease green checkmark'), 0)
		}

		if (WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/Control Your Blood Pressure 100'), 0, FailureHandling.OPTIONAL) ) {

			WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/Control Your Blood Pressure green checkmark'), 0)
		} else {

			WebUI.verifyElementNotPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/Control Your Blood Pressure green checkmark'), 0)
		}

		if (WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/How Kidneys Work 100'), 0, FailureHandling.OPTIONAL) ) {

			WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/How Kidneys Work green checkbox'), 0)
		} else {

			WebUI.verifyElementNotPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/How Kidneys Work green checkbox'), 0)
		}

		if (WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/Making Lifestyle Changes 100'), 0, FailureHandling.OPTIONAL) ) {

			WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/Making Lifestyle Changes green checkbox'), 0)
		} else {

			WebUI.verifyElementNotPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/Making Lifestyle Changes green checkbox'), 0)
		}

		if (WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/Managing Your Blood Sugar 100'), 0, FailureHandling.OPTIONAL) ) {

			WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/Managing Your Blood Sugar green checkbox'), 0)
		} else {

			WebUI.verifyElementNotPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/Managing Your Blood Sugar green checkbox'), 0)
		}

		if (WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/What do Kidneys Do 100'), 0, FailureHandling.OPTIONAL) ) {

			WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/What do Kidneys Do 100 green checkbox'), 0)
		} else {

			WebUI.verifyElementNotPresent(findTestObject('Object Repository/Page_Davita-CKD (1)/What do Kidneys Do 100 green checkbox'), 0)
		}
	}
}

