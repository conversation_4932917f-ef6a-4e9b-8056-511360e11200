package cookbooks_and_eatingoutduides
import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject

import com.kms.katalon.core.annotation.Keyword
import com.kms.katalon.core.checkpoint.Checkpoint
import com.kms.katalon.core.checkpoint.CheckpointFactory
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling
import com.kms.katalon.core.testcase.TestCase
import com.kms.katalon.core.testcase.TestCaseFactory
import com.kms.katalon.core.testdata.TestData
import com.kms.katalon.core.testdata.TestDataFactory
import com.kms.katalon.core.testobject.ObjectRepository
import com.kms.katalon.core.testobject.TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI

import internal.GlobalVariable

import org.openqa.selenium.WebElement
import org.openqa.selenium.WebDriver
import org.openqa.selenium.By

import com.kms.katalon.core.mobile.keyword.internal.MobileDriverFactory
import com.kms.katalon.core.webui.driver.DriverFactory

import com.kms.katalon.core.testobject.RequestObject
import com.kms.katalon.core.testobject.ResponseObject
import com.kms.katalon.core.testobject.ConditionType
import com.kms.katalon.core.testobject.TestObjectProperty

import com.kms.katalon.core.mobile.helper.MobileElementCommonHelper
import com.kms.katalon.core.util.KeywordUtil

import com.kms.katalon.core.webui.exception.WebElementNotFoundException

import cucumber.api.java.en.And
import cucumber.api.java.en.Given
import cucumber.api.java.en.Then
import cucumber.api.java.en.When



class cookbooks_and_eatingoutguides {

	@And("Navigates to CookBooks")
	def cookbooks() {

		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'), 0)

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'))

		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_Cookbooks'), 0)

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_Cookbooks'))
	}

	@And("Validate that SAVE button appears under each recipe thumbnail displayed")
	def cookbook_saved() {

		WebUI.delay(15)

		WebUI.verifyElementPresent(findTestObject("Object Repository/Page_Kidney-Friendly Nutrition/Home-Cooked Holidays") ,0)

		WebUI.takeFullPageScreenshotAsCheckpoint('cookbooks my saved')
	}

	@And("Validate clicking on any of the recipe under COOKBOOK should open up Recipe page in a separate window and user should be able to download and or PRINT the recipe")
	def cookbook_page() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Cook Books recipes'))

		WebUI.delay(15)

		WebUI.switchToWindowIndex('1')

		WebUI.takeFullPageScreenshotAsCheckpoint('cookbooks PDF')

		WebUI.switchToWindowIndex('0')
	}

	@And("Validate Sort By Drop down appears when the user clicks on Cookbooks tab with the following fields")
	def cookbook_sort() {

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Cook Books sort by holder'), 0)

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer serving size dropdown'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Cook Books sort by Alphabetical A-Z'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Cook Books sort by Alphabetical Z-A'), 0)

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Cook Books sort by Alphabetical Z-A'))

		WebUI.takeFullPageScreenshotAsCheckpoint('cookbooks Sort 2')

		WebUI.scrollToPosition(0, 0)

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer serving size dropdown'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Cook Books sort by Alphabetical A-Z'))

		WebUI.takeFullPageScreenshotAsCheckpoint('cookbooks Sort 1')
	}

	@And("Validate that the user sees Quick Tips For Eating Out below the 3 recipes")
	def etg_qtf() {

		WebUI.takeFullPageScreenshotAsCheckpoint('Eating Out guide quick tips')

		WebUI.scrollToPosition(0, 0)
	}

	@And("Validate clicking on any of the eating out guide recipe should open up Recipe page in a separate window and user should be able to download and or PRINT the recipe")
	def etg_details_page() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Cook Books recipes'))

		WebUI.delay(15)

		WebUI.switchToWindowIndex('1')

		WebUI.takeFullPageScreenshotAsCheckpoint('cookbooks PDF')

		WebUI.switchToWindowIndex('0')
	}

	@And("Validate that the user should be able to navigate through all recipes using the horizontal scroll bars")
	def etg_scroll() {}

	@Then("Validate that Quick Tips For Eating Out is NOT displayed when user taps on View All button")
	def etg_qtf_no() {

		WebUI.takeFullPageScreenshotAsCheckpoint('Eating out guide viewmore 2')

		WebUI.scrollToPosition(0, 0)
	}

	@When("Navigates to Eating out Guides")
	def etg() {

		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'), 0)

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'))

		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_Eating Out Guides'), 0)

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_Eating Out Guides'))
	}

	@Then("Validate that user sees three recipes with SAVE button under Eating Out Guides horizontal layout along with View All button")
	def etg_layout() {

		WebUI.delay(10)

		WebUI.verifyElementPresent(findTestObject("Object Repository/Page_Kidney-Friendly Nutrition/Dining out guide test my saved") ,0)

		WebUI.takeFullPageScreenshotAsCheckpoint('eating out guide page')
	}

	@Then("Validate when the user taps on View All button it displays all the available Dining Out Guide recipes on the same page")
	def etg_viewall() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Eating out guide view more'))

		WebUI.takeFullPageScreenshotAsCheckpoint('eating out guide view more page')
	}

	@Then("Validate when the user taps on View All button Sort By  drop down appears with the following fields on Eaating out page")
	def etg_sort() {

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Cook Books sort by holder'), 0)

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer serving size dropdown'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Cook Books sort by Alphabetical A-Z'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Cook Books sort by Alphabetical Z-A'), 0)

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Cook Books sort by Alphabetical Z-A'))

		WebUI.takeFullPageScreenshotAsCheckpoint('cookbooks Sort 2')

		WebUI.scrollToPosition(0, 0)

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer serving size dropdown'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Cook Books sort by Alphabetical A-Z'))

		WebUI.takeFullPageScreenshotAsCheckpoint('cookbooks Sort 1')
	}


	@Then("Validate when the user taps on Save button present is each of the 3 recipes displayed it gets saved in My Saved tab under Eating Out Guides")
	def cookbook_mysaved() {}

	@Then("Validate that user sees all the CookBooks Recipe thumbnails on the same page")
	def cookbook_thumbnail() {

		WebUI.delay(10)

		//WebUI.verifyElementPresent(findTestObject("Repository/Page_Kidney-Friendly Nutrition/Homemade Condiments") ,0)

		WebUI.takeFullPageScreenshotAsCheckpoint('cookbooks')
	}
}