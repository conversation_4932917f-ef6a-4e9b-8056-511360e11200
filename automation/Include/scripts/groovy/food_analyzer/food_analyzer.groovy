package food_analyzer
import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject

import com.kms.katalon.core.annotation.Keyword
import com.kms.katalon.core.checkpoint.Checkpoint
import com.kms.katalon.core.checkpoint.CheckpointFactory
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling
import com.kms.katalon.core.testcase.TestCase
import com.kms.katalon.core.testcase.TestCaseFactory
import com.kms.katalon.core.testdata.TestData
import com.kms.katalon.core.testdata.TestDataFactory
import com.kms.katalon.core.testobject.ObjectRepository
import com.kms.katalon.core.testobject.TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI

import internal.GlobalVariable

import org.openqa.selenium.Keys as Keys

import org.openqa.selenium.WebElement
import org.openqa.selenium.WebDriver
import org.openqa.selenium.By

import com.kms.katalon.core.mobile.keyword.internal.MobileDriverFactory
import com.kms.katalon.core.webui.driver.DriverFactory

import com.kms.katalon.core.testobject.RequestObject
import com.kms.katalon.core.testobject.ResponseObject
import com.kms.katalon.core.testobject.ConditionType
import com.kms.katalon.core.testobject.TestObjectProperty

import com.kms.katalon.core.mobile.helper.MobileElementCommonHelper
import com.kms.katalon.core.util.KeywordUtil

import com.kms.katalon.core.webui.exception.WebElementNotFoundException

import cucumber.api.java.en.And
import cucumber.api.java.en.Given
import cucumber.api.java.en.Then
import cucumber.api.java.en.When



class food_analyzer {
	
	@When("Validate the user can clear the search box when clicking on the X button inside the search box")
	def food_X() {
		
		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/Food Anylizer Search bar'), 'Apple')
		
		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/Food Anylizer Search bar'), Keys.chord(Keys.ENTER))
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer search bar X button'))
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer Recently View cards 1'), 0)
		
	}

	@And("Navigate to Food Analyzer")
	def food_navig() {
		
		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'), 0)
		
		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'))
		
		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_Food Analyzer'), 0)
		
		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_Food Analyzer'))
		
		
	}
	
	
	@And("Validate the last 3 item viewed are displayed")
	def food_recent() {
		
		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/Food Anylizer Search bar'), 'Apple')
		
		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/Food Anylizer Search bar'), Keys.chord(Keys.ENTER))
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer Recently View cards 2'))
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer back to results'))
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer Recently View cards 3'))
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer back to results'))
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer Recently View cards 1'))
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer back to results'))
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer BACK TO FOOD ANALYZER'))
		
		WebUI.scrollToElement(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer Recently View cards 1'), 0)
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer Recently View cards 1'), 0)
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer Recently View cards 2'), 0)
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer Recently View cards 3'), 0)
		
		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer Recently View cards 4'), 0)
	}
	
	
	@And("Validate the Nutrients Per Serving values change when selecting a different serving size")
	def food_nutrition() {}
	
	@Then("Validate the following sections are present")
	def food_main() {
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food Anylizer Search bar'), 0)
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer Look Up Nutrient Data'), 0)
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer Recently View'), 0)
	}
	
	@Then("Validate each item has a title description and the following values")
	def food_des() {
		
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer cards content PHOSPHORUS'), 0)
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer cards content POTASSIUM'), 0)
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer cards content SERVING SIZE'), 0)
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer cards content SODIUM'), 0)
		
	}
	
	@Then("Validate the most recent item is at the top of the list")
	def food_rectop() {
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer card Croissants, apple'), 0)
	}
	
	@Then("Validate the Each Food Item has option for Adding to MY Meal Plan")
	def food_plus() {
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Croissants, apple add to my meal plan'), 0)
	}
	
	@Then("Validate the user can search for any food")
	def food_search() {
		
		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/Food Anylizer Search bar'), 'Apple')
		
		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/Food Anylizer Search bar'), Keys.chord(Keys.ENTER))
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer card Croissants, apple'), 0)
	}
	
	@Then("Validate no results found is displayed if there is no food matching the search term")
	def food_nores() {
		
		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/Food Anylizer Search bar'), 'A')
		
		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/Food Anylizer Search bar'), Keys.chord(Keys.ENTER))
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/div_No results found'),0 )
	}
	
	@Then("Validate the user comes back to the Food Analyser main page when cliking on the Back to Food Analyser button")
	def food_backbut() {
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer BACK TO FOOD ANALYZER'))
		
		
	}
	
	@Then("Validate the number of results is displayed")
	def food_count() {
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food Analyzer results count'), 0)
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer BACK TO FOOD ANALYZER'))
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer Recently View cards 1'), 0)
	}
	
	@Then("Validate the Food Analyzer Tips section is displayed on the right side of the results")
	def food_tips() {
		
		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/Food Anylizer Search bar'), 'Apple')
		
		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/Food Anylizer Search bar'), Keys.chord(Keys.ENTER))
		
		WebUI.takeFullPageScreenshotAsCheckpoint('Food Analyzer Tips section')
		
	}
	
	@Then("Validate a new tab is open and the user gets redirected to the USDA site when clicking on the View USDA Website in the Food Analyzer Tips section")
	def food_USDA() {
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer USDA button'))
		
		WebUI.switchToWindowIndex('1')
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer USDA web'), 0)
		
		WebUI.switchToWindowIndex('0')
		
	}
	
	@Then("Validate the item has following fields")
	def food_detailFields() {
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer card Croissants, apple'))
		
		WebUI.takeFullPageScreenshotAsCheckpoint('Food Analyzer details page')
		
		WebUI.scrollToPosition(0, 0)
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer serving size dropdown'))
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Food analyzer serving size item'))
		
		WebUI.takeFullPageScreenshotAsCheckpoint('Food Analyzer details page 2')
		
	}

}