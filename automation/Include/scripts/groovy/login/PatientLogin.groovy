package login
import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject

import com.kms.katalon.core.annotation.Keyword
import com.kms.katalon.core.checkpoint.Checkpoint
import com.kms.katalon.core.checkpoint.CheckpointFactory
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling
import com.kms.katalon.core.testcase.TestCase
import com.kms.katalon.core.testcase.TestCaseFactory
import com.kms.katalon.core.testdata.TestData
import com.kms.katalon.core.testdata.TestDataFactory
import com.kms.katalon.core.testobject.ObjectRepository
import com.kms.katalon.core.testobject.TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI

import internal.GlobalVariable

import org.openqa.selenium.WebElement
import org.openqa.selenium.WebDriver
import org.openqa.selenium.By

import com.kms.katalon.core.mobile.keyword.internal.MobileDriverFactory
import com.kms.katalon.core.webui.driver.DriverFactory

import com.kms.katalon.core.testobject.RequestObject
import com.kms.katalon.core.testobject.ResponseObject
import com.kms.katalon.core.testobject.ConditionType
import com.kms.katalon.core.testobject.TestObjectProperty

import com.kms.katalon.core.mobile.helper.MobileElementCommonHelper
import com.kms.katalon.core.util.KeywordUtil

import com.kms.katalon.core.webui.exception.WebElementNotFoundException

import cucumber.api.java.en.And
import cucumber.api.java.en.Given
import cucumber.api.java.en.Then
import cucumber.api.java.en.When

class PatientLogin {

	@Given("The Pateint Login page is loaded successfully")
	def Login_Page_Open() {
		WebUI.openBrowser('')

		WebUI.navigateToUrl('https://ckd-04bdd.patient-np.davita.com')
	}

	@When("A Pateint enters the username & Password")
	def Enter_Username_and_Paasword() {

		WebUI.setText(findTestObject('Object Repository/CKD-Login Page/input_Sign in or create an account_mat-input-0'), 'DHAPATEL')

		WebUI.setEncryptedText(findTestObject('Object Repository/CKD-Login Page/input_Username or Email_mat-input-1'), 'wam7MQ9/3PCV54S2HpRUxA==')
	}

	@When("A Pateint enters the username & and wrong Password")
	def Enter_Username_and_wrong_Paasword() {

		WebUI.setText(findTestObject('Object Repository/CKD-Login Page/input_Sign in or create an account_mat-input-0'), 'DHAPATEL')

		WebUI.setEncryptedText(findTestObject('Object Repository/CKD-Login Page/input_Username or Email_mat-input-1'), '4KhbKy5Dhag=')
	}

	@Then("Pateint Homepage gets loaded successfully")
	def Home_Page_gets_Open() {
		WebUI.click(findTestObject('Object Repository/CKD-Login Page/button_LOGIN'))
		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_DaVita-CKD/div_Home verify element'), 0)
		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_DaVita-CKD/div_Home verify element'), 0)
		WebUI.closeBrowser()
	}

	@Then("Pateint Error message should pop-up")
	def Validate_the_Error_popup() {
		WebUI.click(findTestObject('Object Repository/CKD-Login Page/button_LOGIN'))
		WebUI.click(findTestObject('Object Repository/CKD-Login Page/div_Error  Username andor password is invalid'))
		WebUI.closeBrowser()
	}
}