package login
import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject

import com.kms.katalon.core.annotation.Keyword
import com.kms.katalon.core.checkpoint.Checkpoint
import com.kms.katalon.core.checkpoint.CheckpointFactory
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling
import com.kms.katalon.core.testcase.TestCase
import com.kms.katalon.core.testcase.TestCaseFactory
import com.kms.katalon.core.testdata.TestData
import com.kms.katalon.core.testdata.TestDataFactory
import com.kms.katalon.core.testobject.ObjectRepository
import com.kms.katalon.core.testobject.TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI

import internal.GlobalVariable

import org.openqa.selenium.WebElement
import org.openqa.selenium.WebDriver
import org.openqa.selenium.By

import com.kms.katalon.core.mobile.keyword.internal.MobileDriverFactory
import com.kms.katalon.core.webui.driver.DriverFactory

import com.kms.katalon.core.testobject.RequestObject
import com.kms.katalon.core.testobject.ResponseObject
import com.kms.katalon.core.testobject.ConditionType
import com.kms.katalon.core.testobject.TestObjectProperty

import com.kms.katalon.core.mobile.helper.MobileElementCommonHelper
import com.kms.katalon.core.util.KeywordUtil

import com.kms.katalon.core.webui.exception.WebElementNotFoundException

import cucumber.api.java.en.And
import cucumber.api.java.en.Given
import cucumber.api.java.en.Then
import cucumber.api.java.en.When

class TeammateLogin {

	@Given("The Teammate Login page is loaded successfully")
	def Login_Page_Open() {
		
		WebUI.openBrowser('')

		WebUI.navigateToUrl('https://ckd-04bdd.patient-np.davita.com/admin')
	}

	@When("A Teammate enters the username & Password")
	def Enter_Username_and_Paasword() {

		WebUI.setText(findTestObject('Object Repository/Page_Sign On/input_Username_pf.username'), 'DHAPATEL')

		WebUI.setEncryptedText(findTestObject('Object Repository/Page_Sign On/input_Password_pf.pass'), 'LooyJurdTRgZg6sIKPZ/zQ==')
	}

	@When("A Teammate enters the username & and wrong Password")
	def Enter_Username_and_wrong_Paasword() {

		WebUI.setText(findTestObject('Object Repository/Page_Sign On/input_Username_pf.username'), 'DHAPATEL')

		WebUI.setEncryptedText(findTestObject('Object Repository/Page_Sign On/input_Password_pf.pass'), 'vdAqDF2nWpEpl0CToEw2MA==')
	}

	@Then("Teammate Homepage gets loaded successfully")
	def Home_Page_gets_Open() {
		
		WebUI.click(findTestObject('Object Repository/Page_Sign On/font_Continue'))
		
	    WebUI.waitForElementPresent(findTestObject('Object Repository/Page_Sign On/Teammate Homepage'), 0)
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Sign On/Teammate Homepage'), 0)

		WebUI.closeBrowser()
	}

	@Then("Teammate Login Error message should pop-up")
	def Validate_the_Error_popup() {
		
		WebUI.click(findTestObject('Object Repository/Page_Sign On/font_Continue'))
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Sign On/div_We didnt recognize the username or pass_2deb35'), 0)
		
		WebUI.closeBrowser()
	}
}