package my_meal_plan
import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject

import com.kms.katalon.core.annotation.Keyword
import com.kms.katalon.core.checkpoint.Checkpoint
import com.kms.katalon.core.checkpoint.CheckpointFactory
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling
import com.kms.katalon.core.testcase.TestCase
import com.kms.katalon.core.testcase.TestCaseFactory
import com.kms.katalon.core.testdata.TestData
import com.kms.katalon.core.testdata.TestDataFactory
import com.kms.katalon.core.testobject.ObjectRepository
import com.kms.katalon.core.testobject.TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI

import internal.GlobalVariable

import org.openqa.selenium.WebElement
import org.openqa.selenium.WebDriver
import org.openqa.selenium.By

import com.kms.katalon.core.mobile.keyword.internal.MobileDriverFactory
import com.kms.katalon.core.webui.driver.DriverFactory

import com.kms.katalon.core.testobject.RequestObject
import com.kms.katalon.core.testobject.ResponseObject
import com.kms.katalon.core.testobject.ConditionType
import com.kms.katalon.core.testobject.TestObjectProperty

import com.kms.katalon.core.mobile.helper.MobileElementCommonHelper
import com.kms.katalon.core.util.KeywordUtil

import com.kms.katalon.core.webui.exception.WebElementNotFoundException
import org.openqa.selenium.Keys as Keys

import cucumber.api.java.en.And
import cucumber.api.java.en.Given
import cucumber.api.java.en.Then
import cucumber.api.java.en.When



class my_meal_plan {


	@And("Navigates to My Meal Plan")
	def my_meal() {

		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'), 0)

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'))

		WebUI.waitForElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan navigation'), 0)

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan navigation'))
	}


	@And("clicking on Cancel button should close the Modal")
	def Modal_close() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan cancel button'))
	}


	@When("clicks on add a breakfast Recipes than filter Breakfast & Brunch should be applied")
	def breakfast() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Add Break fast Recipies button'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Filter checkbox'), 0)

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan navigation'))
	}

	@When("clicks on add a snacks recipes than filter Appetizers & Snacks should be applied")
	def snacks() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Add Snack Recipies button'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Filter checkbox'), 0)
	}

	@When("clicks on add button for any Recipes should open the Modal containing all of the necessary fields")
	def recipes() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Add Snack Recipies button'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Addictive Pretzels add to my meal plan'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Heading'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan recipes name Addictive Pritzel'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Servings Recipes'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan date input'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan cancel button'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan add button'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Meal type Recipes'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Health Advisory paragraph'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Date Picker'), 0)
	}

	@Then("user should be able to see the add recipes buttons and clicking on should redirect to Recipes menu")
	def add_recipies() {

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Add Break fast Recipies button'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Add Dinner Recipies button'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Add Snack Recipies button'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Add lunch Recipies button'), 0)

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Add Dinner Recipies button'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan navigation'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Add lunch Recipies button'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan navigation'))
	}

	@Then("validate that User can create the My Meal Plan From Recipes")
	def new_plan() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Addictive Pretzels add to my meal plan'))

		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan date input'), '09012023')

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan add button'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan added message'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Addictive Pretzels My Meal plan present'), 0)
	}



	@Then("User should be able to see the informations in Daily Nutrition Tracking")
	def info_tab() {

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CALCIUM Recipes'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CALORIES Recipes'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CARBOHYDRATES Recipes'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count FAT Recipes'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count FIBER Recipes'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count PHOSPHORUS Recipes'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count Pottasium Recipes'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count PROTEIN Recipes'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count Sodium Recipes'), 0)
	}

	@Then("user should be able to edit any existing the Recipes My Meal Plan")
	def edit_plan() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Date Picker'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan page Year selector'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan year 2023'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan Month Selector'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan date selector'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Edit Button'))

		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan date input'), '09022023')

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Servings Recipes'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Edit servings'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Meal type Recipes'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Edit meal type Lunch'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Save Button'))
	}

	@Then("User should be see the updated info for edited My Meal Plan and Daily Nutrition Tracking")
	def edited_info() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Date Picker'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan page Year selector'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan year 2023'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan Month Selector'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan date selector 2'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Addictive Pretzels My Meal plan present'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CALCIUM Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CALORIES Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CARBOHYDRATES Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count FAT Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count FIBER Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count PHOSPHORUS Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count Pottasium Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count PROTEIN Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count Sodium Recipes'), 0)
	}

	@Then("user should be able to Delete any existing the Recipes My Meal Plan")
	def delete_plan() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Date Picker'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan page Year selector'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan year 2023'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan Month Selector'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan date selector 2'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Delete Button'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan cancel button'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Delete Button'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Delete Button confirm'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Deleted message'), 0)
	}

	@Then("User should be see the updated info after deleted My Meal Plan in Daily Nutrition Tracking")
	def deleted_info() {

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CALCIUM Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CALORIES Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CARBOHYDRATES Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count FAT Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count FIBER Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count PHOSPHORUS Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count Pottasium Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count PROTEIN Recipes'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count Sodium Recipes'), 0)
	}


	@Then("User should be able to add Recipes to My Meal Plan")
	def from_recipies() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Date Picker'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan page Year selector'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan year 2023'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan Month Selector'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan date selector'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Addictive Pretzels My Meal plan present'), 0)
	}

	@Then("User should be able to see already added My Meal Plan")
	def verify_mymealplan() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Date Picker'))
	}

	@And("Navigates to Food Analyzer")
	def food_analyzer() {

		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'), 0)

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_Food Analyzer'))
	}

	@When("clicks on add button for any Food Item should open the Modal containing all of the necessary fields")
	def add_modal() {

		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/Food Anylizer Search bar'), 'Apple')

		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/Food Anylizer Search bar'), Keys.chord(Keys.ENTER))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Croissants, apple add to my meal plan'))

		//WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Heading'), 0)

		//WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan recipes name'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Servings Food Analyzer'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan date input'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Servings Food Analyzer 2'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan cancel button'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan add button'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Meal type Recipes'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Health Advisory paragraph'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Date Picker'), 0)
	}

	@Then("validate that User can create the My Meal Plan from any Food Item")
	def add_from_food_analyzer() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Croissants, apple add to my meal plan'))

		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan date input'), '09012023')

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan add button'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan added message'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Croissants, apple My Meal plan present'), 0)
	}

	@Then("User should be able to see the informations of the added Food Item in Daily Nutrition Tracking")
	def tracking_info_added() {

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CALCIUM Food Analyzer'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CALORIES Food Analyzer'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CARBOHYDRATES Food Analyzer'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count FAT Food Analyzer'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count FIBER Food Analyzer'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count PHOSPHORUS Food Analyzer'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count Pottasium Food Analyzer'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count PROTEIN Food Analyzer'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count Sodium Food Analyzer'), 0)
	}

	@Then("user should be able to edit any existing Food Analyzer My Meal Plan")
	def edit_food_analyzer() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Date Picker'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan page Year selector'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan year 2023'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan Month Selector'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan date selector'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Edit Button'))

		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan date input'), '09022023')

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Servings Food Analyzer'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Edit servings'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Meal type Food Analyzer'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Edit meal type Lunch'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Save Button'))
	}

	@Then("User should be see the updated info for edited Food Analyzer My Meal Plan and Daily Nutrition Tracking")
	def tracking_info_edited() {

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CALCIUM Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CALORIES Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CARBOHYDRATES Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count FAT Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count FIBER Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count PHOSPHORUS Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count Pottasium Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count PROTEIN Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count Sodium Food Analyzer'), 0)
	}

	@Then("user should be able to Delete any existing Food Analyzer My Meal Plan")
	def delete_food_analyzer() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Date Picker'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan page Year selector'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan year 2023'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan Month Selector'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My meal plan date selector 2'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Delete Button'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan cancel button'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Delete Button'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Delete Button confirm'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/My Meal Plan Deleted message'), 0)
	}

	@Then("User should be see the updated info after deleted Food Analyzer My Meal Plan in Daily Nutrition Tracking")
	def tracking_info_deleted() {


		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CALCIUM Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CALORIES Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count CARBOHYDRATES Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count FAT Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count FIBER Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count PHOSPHORUS Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count Pottasium Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count PROTEIN Food Analyzer'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Nutriton Count Sodium Food Analyzer'), 0)
	}
}