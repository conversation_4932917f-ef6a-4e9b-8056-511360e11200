package my_saved
import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject

import com.kms.katalon.core.annotation.Keyword
import com.kms.katalon.core.checkpoint.Checkpoint
import com.kms.katalon.core.checkpoint.CheckpointFactory
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling
import com.kms.katalon.core.testcase.TestCase
import com.kms.katalon.core.testcase.TestCaseFactory
import com.kms.katalon.core.testdata.TestData
import com.kms.katalon.core.testdata.TestDataFactory
import com.kms.katalon.core.testobject.ObjectRepository
import com.kms.katalon.core.testobject.TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI

import internal.GlobalVariable

import org.openqa.selenium.WebElement
import org.openqa.selenium.WebDriver
import org.openqa.selenium.By

import com.kms.katalon.core.mobile.keyword.internal.MobileDriverFactory
import com.kms.katalon.core.webui.driver.DriverFactory

import com.kms.katalon.core.testobject.RequestObject
import com.kms.katalon.core.testobject.ResponseObject
import com.kms.katalon.core.testobject.ConditionType
import com.kms.katalon.core.testobject.TestObjectProperty

import com.kms.katalon.core.mobile.helper.MobileElementCommonHelper
import com.kms.katalon.core.util.KeywordUtil

import com.kms.katalon.core.webui.exception.WebElementNotFoundException

import cucumber.api.java.en.And
import cucumber.api.java.en.Given
import cucumber.api.java.en.Then
import cucumber.api.java.en.When



class My_saved {



	@And("Navigate to My Saved")
	def my_saved() {

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Span arrow right'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_My Saved'))
	}

	@Then("Validate no results found is displayed if the user doesn't have anything saved")
	def no_results() {
		
		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Video Filter'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/div_No results found'),0 )
	}


	@Then("Validate the user can saved any of the above following categories")
	def save_items() {

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Span arrow left'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Nav'))

		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Diet Type sort'), 0)

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Diet Type sort'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/CKD No Dai sort'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Acai Berry Smoothie Bowl my saved'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/3-2-1 Cupcakes'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Addictive Pretzels my saved'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_Videos'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Entress sort'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Chiken Marsala my saved'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_Eating Out Guides'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Dining out guide test my saved'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_Cookbooks'))

		WebUI.scrollToElement(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Homemade Condiments'), 0)

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Home-Cooked Holidays'))

		WebUI.scrollToElement(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Diet and Nutrition Header'), 0)

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Span arrow right'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_My Saved'))
	}

	@Then("Validate only 3 items per row are displayed")
	def item_row() {
	}

	@Then("Validate the user can remove items from the saved section and removed items remain on the page until the user goes to another page or refreshes")
	def removed_items() {

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Acai Berry Smoothie Bowl my saved added'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Addictive Pretzels my saved added'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/3-2-1 Cupcakes added'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Chiken Marsala my saved added'))
		
		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Eating out Filter'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Dining out guide test my saved added'))
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Dining out guide test my saved') ,0)
		
		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Eating out Filter'))
		
		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Cookbook filter'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Home-Cooked Holidays added'))
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Home-Cooked Holidays') ,0)
		
		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Cookbook filter'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Acai Berry Smoothie Bowl my saved') ,0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/3-2-1 Cupcakes') ,0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Addictive Pretzels my saved') ,0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Chiken Marsala my saved') ,0)

	    WebUI.scrollToElement(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Diet and Nutrition Header'), 0)

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_Food Analyzer'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_My Saved'))
		
		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Video Filter'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/div_No results found'),0 )
	}

	@Then("Valite the user can see the item details when cliking on the card")
	def items_details() {

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Addictive Pretzels details'))

		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Addictive Pretzels details') ,0)

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Go back to recipes button'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Span arrow right'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_My Saved'))
	}

	@Then("Validate the user can select one or more categories")
	def categories_select() {

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Video Filter'))

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Eating out Filter'))
	}

	@Then("Validate the user can collapse and uncollapse the filter options")
	def filter_options() {

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Categories filter oprion'))
	}

	@Then("Validate the user can clear the filters")
	def filter_cleare() {

		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Filter Cleare button'))
		
		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter count'), 0)
	}
}