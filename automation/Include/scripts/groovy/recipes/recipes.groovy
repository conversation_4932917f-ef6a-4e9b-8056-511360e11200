package recipes
import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject

import com.kms.katalon.core.annotation.Keyword
import com.kms.katalon.core.checkpoint.Checkpoint
import com.kms.katalon.core.checkpoint.CheckpointFactory
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling
import com.kms.katalon.core.testcase.TestCase
import com.kms.katalon.core.testcase.TestCaseFactory
import com.kms.katalon.core.testdata.TestData
import com.kms.katalon.core.testdata.TestDataFactory
import com.kms.katalon.core.testobject.ObjectRepository
import com.kms.katalon.core.testobject.TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI

import internal.GlobalVariable

import org.openqa.selenium.WebElement
import org.openqa.selenium.WebDriver
import org.openqa.selenium.By

import com.kms.katalon.core.mobile.keyword.internal.MobileDriverFactory
import com.kms.katalon.core.webui.driver.DriverFactory

import com.kms.katalon.core.testobject.RequestObject
import com.kms.katalon.core.testobject.ResponseObject
import com.kms.katalon.core.testobject.ConditionType
import com.kms.katalon.core.testobject.TestObjectProperty

import com.kms.katalon.core.mobile.helper.MobileElementCommonHelper
import com.kms.katalon.core.util.KeywordUtil

import com.kms.katalon.core.webui.exception.WebElementNotFoundException

import org.openqa.selenium.Keys as Keys

import cucumber.api.java.en.And
import cucumber.api.java.en.Given
import cucumber.api.java.en.Then
import cucumber.api.java.en.When



class recipes {

	@And("Navigate to Recipes")
	def recipes_page() {

		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'), 0)

		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'))

		WebUI.waitForElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Nav'), 0)

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Nav'))
	}

	@And("Back to Recipes link It should take user back to the original Recipe page")
	def back_to_recipes() {

		WebUI.scrollToElement(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Details page Preparation'), 0)

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Nav back'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Dieat and Nutition Heading'), 0)
	}

	@And("Sort By drop down should appear once hit the Search button with different search options")
	def sort_by() {

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes sort'), 0)
	}

	@And("Validate that the user is able to sort the filter the recipe based on his or her selection and filter count should appear in front of the filter applied")
	def filter_option_choosing() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter category'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Breakast'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Sancks'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter count'), 0)
	}

	@Then("Validate the 4 categorized recipe sections appear in horizontal rows with 3 recipe items displayed under each section")
	def recipes_catagories() {

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Categories 1'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Categories 2'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Categories 3'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Categories 4'), 0)

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Categories 5'), 0)
	}

	@Then("Validate that when the user clicks on any recipe, it displays the DETAILED recipe page with all the recipe specific info and the following fields")
	def recipes_details_page() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter category'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Sancks'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Addictive Pretzels'))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Details page PRINT icon'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Details page save'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Details page PLAN icon'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Addictive Pretzels heading'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Details page Nutrition Details'), 0)

		WebUI.scrollToElement(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Details page Ingredients'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Details page Ingredients'), 0)

		WebUI.scrollToElement(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Details page Preparation'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Details page Preparation'), 0)

		WebUI.scrollToElement(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Details page Helpful Hints'), 0)

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Details page Helpful Hints'), 0)
	}

	@Then("Validate that user should be able to search for any recipe by typing it in the search bar and hitting Enter key")
	def hit_enter() {

		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Search bar'), 'Apple')

		WebUI.setText(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Search bar'), Keys.chord(Keys.ENTER))

		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Search results'), 0)
	}

	@Then("Validate that the user is able to sort the recipes based on his or her choice of sorting as described in above step")
	def sort_recipes() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes sort'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes sort Calories (high to low)'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes sort'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes sort Calories (low to high)'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes sort'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes sort Carbohydrates (high to low)'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes sort'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes sort Carbohydrates (low to high)'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes sort'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes sort Protein (high to low)'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes sort'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes sort Protein (low to high)'))
	}

	@Then("Validate Filter section appears with the following entries with drop down sub menus")
	def recipes_filter() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Dish Type'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Dish Type'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter category'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter cleare'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Cooking Method'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Cooking Method'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Cuisine'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Cuisine'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Diet Type'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Diet Type'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Tags'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Tags'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Has Recipe Photo - Copy'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Holiday'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Holiday'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Servings'))

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter Servings'))
	}

	@When("clicking on CLEAR button should remove all the filters applied and all recipes should appear")
	def filter_cleare() {

		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter cleare'))

		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter count'), 0)
	}
}