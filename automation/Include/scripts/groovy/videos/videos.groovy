package videos
import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject

import com.kms.katalon.core.annotation.Keyword
import com.kms.katalon.core.checkpoint.Checkpoint
import com.kms.katalon.core.checkpoint.CheckpointFactory
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling
import com.kms.katalon.core.testcase.TestCase
import com.kms.katalon.core.testcase.TestCaseFactory
import com.kms.katalon.core.testdata.TestData
import com.kms.katalon.core.testdata.TestDataFactory
import com.kms.katalon.core.testobject.ObjectRepository
import com.kms.katalon.core.testobject.TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI

import internal.GlobalVariable

import org.openqa.selenium.WebElement
import org.openqa.selenium.WebDriver
import org.openqa.selenium.By

import com.kms.katalon.core.mobile.keyword.internal.MobileDriverFactory
import com.kms.katalon.core.webui.driver.DriverFactory

import com.kms.katalon.core.testobject.RequestObject
import com.kms.katalon.core.testobject.ResponseObject
import com.kms.katalon.core.testobject.ConditionType
import com.kms.katalon.core.testobject.TestObjectProperty

import com.kms.katalon.core.mobile.helper.MobileElementCommonHelper
import com.kms.katalon.core.util.KeywordUtil

import com.kms.katalon.core.webui.exception.WebElementNotFoundException

import cucumber.api.java.en.And
import cucumber.api.java.en.Given
import cucumber.api.java.en.Then
import cucumber.api.java.en.When



class videos {
	
	@And("Navigate to Videos")
	def videos_nav() {
		
		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'), 0)
		
		WebUI.click(findTestObject('Object Repository/Page_DaVita-CKD/div_Nutrition'))
		
		WebUI.waitForElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_Videos'), 0)
		
		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/a_Videos'))
	}
	
	@Then("Validate the user can see 4 of the following categories in the main page")
	def videos_cat() {
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Categories 1'), 0)
		
	    WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Categories 2'), 0)
		
		WebUI.scrollToElement(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Categories 4'), 0)
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Categories 3'), 0)
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Categories 4'), 0)
		
		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Categories 5'), 0)
		
		WebUI.scrollToPosition(0, 0)
	}
	
	@Then("Validate each of the categories has 3 videos")
	def each_cate() {
		
		
	}
	
    @And("Validate each card has the time of the videos")
	def time_videos() {
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Videos Time count'),0 )
	}
	
	@And("Validate each card has a button to add to favorites on the bottom right corner and clicking heart button turns to Red after cliking on it")
	def my_fav() {
		
		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Entress sort'))
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Chiken Marsala my saved') ,0)
		
		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Chiken Marsala my saved'))
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Chiken Marsala my saved added') ,0)
		
		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Chiken Marsala my saved added'))
		
		
	}
	
	@Then("Validate the videos are reproduce automatically")
	def video_repo() {
		
		WebUI.click(findTestObject('Object Repository/Page_Kidney-Friendly Nutrition/Entress sort'))
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Videos Chiken Marsala'))
		
		WebUI.delay(10)
		
	}
	
	@And("Validate the user can return to the categories when cliking on Back to videos")
	def back_videos() {
		
		WebUI.scrollToPosition(0, 0)
		
		WebUI.doubleClick(findTestObject('Object Repository/CKD Diet and Nutrition/Back to Videos'))
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Categories 1'), 0)
	
	}
	
	@And("Validate each video has a title and an about section")
	def title_about() {
		
		WebUI.scrollToElement(findTestObject('Object Repository/CKD Diet and Nutrition/Videos dteails page about this videos'), 0)
		
		WebUI.waitForElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Videos Chiken Marsala details page title'), 0)
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Videos Chiken Marsala details page title'),0 )
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Videos dteails page about this videos'),0 )
		
	}
	
	@And("Validate the number of categories selected is display next to the Category label")
	def count_filters() {
		
		WebUI.verifyElementPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter count'), 0)
	}
	
	@And("Validate the user can collapse or uncollapse the filter options")
	def filter_collaps() {
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter category'))
		
	}

	@Then("Validate the user can clear the filters Default numbr of videos should be displayed after clicking on Clear")
	def cleare_video() {
		
		WebUI.click(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter cleare'))
		
		WebUI.verifyElementNotPresent(findTestObject('Object Repository/CKD Diet and Nutrition/Recipes Filter count'), 0)
	}
	
}