package arcadeDev

import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject
import static com.kms.katalon.core.testobject.ObjectRepository.findWindowsObject

import com.kms.katalon.core.annotation.Keyword
import com.kms.katalon.core.checkpoint.Checkpoint
import com.kms.katalon.core.cucumber.keyword.CucumberBuiltinKeywords as CucumberKW
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling
import com.kms.katalon.core.testcase.TestCase
import com.kms.katalon.core.testdata.TestData
import com.kms.katalon.core.testobject.TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI
import com.kms.katalon.core.windows.keyword.WindowsBuiltinKeywords as Windows

import internal.GlobalVariable

public class DevPageLoad {

	@Keyword

	public void geturlpageload() {
		WebUI.openBrowser('')

		//WebUI.navigateToUrl('https://arcade-frontend-94772.patient-np.davita.com/')
		WebUI.navigateToUrl(GlobalVariable.G_ArDevUrl)

		WebUI.maximizeWindow()

		WebUI.refresh()
		//  if(WebUI.verifyElementText(findTestObject('Object Repository/Page_My Account/span_CREATE ACCOUNT'), 'CREATE ACCOUNT'))
		//	 {
		WebUI.verifyElementText(findTestObject('Object Repository/Page_My Account/span_CREATE ACCOUNT'), 'CREATE ACCOUNT')
		//    }
		//	else
		//	{
		//	WebUI.refresh()

		//	WebUI.verifyElementText(findTestObject('Object Repository/Page_My Account/span_CREATE ACCOUNT'), 'CREATE ACCOUNT')
		//   }
	}
}