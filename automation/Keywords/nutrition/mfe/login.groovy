package nutrition.mfe;
package nutrition_mfe

import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject
import static com.kms.katalon.core.testobject.ObjectRepository.findWindowsObject

import com.kms.katalon.core.annotation.Keyword
import com.kms.katalon.core.checkpoint.Checkpoint
import com.kms.katalon.core.cucumber.keyword.CucumberBuiltinKeywords as CucumberKW
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling
import com.kms.katalon.core.testcase.TestCase
import com.kms.katalon.core.testdata.TestData
import com.kms.katalon.core.testobject.TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI
import com.kms.katalon.core.windows.keyword.WindowsBuiltinKeywords as Windows

import internal.GlobalVariable

public class login {
	
	@Keyword
	public void login() {


WebUI.openBrowser('https://frontend-94772-nutrition-micro-frontend.patient-np.davita.com/')

//WebUI.maximizeWindow()

WebUI.delay(10)

WebUI.setText(findTestObject('Object Repository/Page_My Account/input_Sign in or create an account_mat-input-0'), 'florstew2')

WebUI.click(findTestObject('Object Repository/Page_My Account/div_Password'))

WebUI.setEncryptedText(findTestObject('Object Repository/Page_My Account/input_Username or Email_mat-input-1'), 'E96uGpFvSbCEGPub+YLxZA==')

WebUI.click(findTestObject('Object Repository/Page_My Account/button_LOGIN'))
	
       }
}
