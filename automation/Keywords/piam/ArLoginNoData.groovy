package piam

import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject
import static com.kms.katalon.core.testobject.ObjectRepository.findWindowsObject

import com.kms.katalon.core.annotation.Keyword
import com.kms.katalon.core.checkpoint.Checkpoint
import com.kms.katalon.core.cucumber.keyword.CucumberBuiltinKeywords as CucumberKW
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling
import com.kms.katalon.core.testcase.TestCase
import com.kms.katalon.core.testdata.TestData
import com.kms.katalon.core.testobject.TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI
import com.kms.katalon.core.windows.keyword.WindowsBuiltinKeywords as Windows

import internal.GlobalVariable

public class ArLoginNoData {

	@Keyword
	public void ulogin() {

		WebUI.setText(findTestObject('Object Repository/PIAM/input_Username or Email_mat-input-0'), GlobalVariable.G_UsernameNoData)

		WebUI.setText(findTestObject('Object Repository/PIAM/input_Password_mat-input-1'), GlobalVariable.G_PasswordNoData)

		WebUI.click(findTestObject('Object Repository/PIAM/span_LOGIN'))

		WebUI.delay(15)
		//WebUI.refresh()
	}
}
