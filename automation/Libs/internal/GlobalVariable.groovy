package internal

import com.kms.katalon.core.configuration.RunConfiguration
import com.kms.katalon.core.main.TestCaseMain


/**
 * This class is generated automatically by Katalon Studio and should not be modified or deleted.
 */
public class GlobalVariable {
     
    /**
     * <p></p>
     */
    public static Object G_Timeout
     
    /**
     * <p></p>
     */
    public static Object G_SiteURL
     
    /**
     * <p></p>
     */
    public static Object G_ShortTimeOut
     
    /**
     * <p></p>
     */
    public static Object G_DevArcadeUrl
     
    /**
     * <p></p>
     */
    public static Object G_uname
     
    /**
     * <p></p>
     */
    public static Object G_pwd
     
    /**
     * <p></p>
     */
    public static Object base_url
     
    /**
     * <p></p>
     */
    public static Object G_Url
     
    /**
     * <p></p>
     */
    public static Object G_Username
     
    /**
     * <p></p>
     */
    public static Object G_Password
     
    /**
     * <p></p>
     */
    public static Object G_UsernameNoData
     
    /**
     * <p></p>
     */
    public static Object G_PasswordNoData
     

    static {
        try {
            def selectedVariables = TestCaseMain.getGlobalVariables("default")
			selectedVariables += TestCaseMain.getGlobalVariables(RunConfiguration.getExecutionProfile())
            selectedVariables += TestCaseMain.getParsedValues(RunConfiguration.getOverridingParameters(), selectedVariables)
    
            G_Timeout = selectedVariables['G_Timeout']
            G_SiteURL = selectedVariables['G_SiteURL']
            G_ShortTimeOut = selectedVariables['G_ShortTimeOut']
            G_DevArcadeUrl = selectedVariables['G_DevArcadeUrl']
            G_uname = selectedVariables['G_uname']
            G_pwd = selectedVariables['G_pwd']
            base_url = selectedVariables['base_url']
            G_Url = selectedVariables['G_Url']
            G_Username = selectedVariables['G_Username']
            G_Password = selectedVariables['G_Password']
            G_UsernameNoData = selectedVariables['G_UsernameNoData']
            G_PasswordNoData = selectedVariables['G_PasswordNoData']
            
        } catch (Exception e) {
            TestCaseMain.logGlobalVariableError(e)
        }
    }
}
