<?xml version="1.0" encoding="UTF-8"?>
<WebElementEntity>
   <description></description>
   <name>Recipes Filter Sancks</name>
   <tag></tag>
   <elementGuidId>cbac8154-c24a-44e6-a606-2e86202a848d</elementGuidId>
   <selectorCollection>
      <entry>
         <key>XPATH</key>
         <value>//*[contains(text(),' Appetizers &amp; Snacks ')]</value>
      </entry>
      <entry>
         <key>CSS</key>
      </entry>
   </selectorCollection>
   <selectorMethod>XPATH</selectorMethod>
   <useRalativeImagePath>true</useRalativeImagePath>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>tag</name>
      <type>Main</type>
      <value>div</value>
      <webElementGuid>ed241596-1f7c-4c2c-835c-1a9b80431277</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>class</name>
      <type>Main</type>
      <value>item-percentage</value>
      <webElementGuid>350d0367-8113-4869-be64-d272f285425d</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>text</name>
      <type>Main</type>
      <value> 100 % </value>
      <webElementGuid>b0616895-65b8-429e-8bd8-d40607cc0f21</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath</name>
      <type>Main</type>
      <value>id(&quot;scrollToTop&quot;)/div[@class=&quot;main-content&quot;]/app-education-category[1]/section[@class=&quot;education-category-wrapper&quot;]/section[@class=&quot;tile-container&quot;]/main[@class=&quot;content-container&quot;]/section[@class=&quot;item-container&quot;]/div[@class=&quot;item-detail&quot;]/div[@class=&quot;item-percentage&quot;]</value>
      <webElementGuid>f2578d40-8f1c-4ad9-a59f-e5270958375f</webElementGuid>
   </webElementProperties>
</WebElementEntity>
