{"name": "messaging-micro-frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve -c $npm_config_env", "build": "ng build -c $npm_config_env", "watch": "ng build --watch --configuration development", "prettier": "prettier --write .", "pretty-quick": "pretty-quick --staged --ignore-path=.prettierignore", "test": "ng test", "lint": "ng lint", "i18n": "ng extract-i18n --output-path src/locale", "run:all": "node node_modules/@angular-architects/module-federation/src/server/mf-dev-server.js", "prepare": "cd ../ && husky install ./.husky"}, "private": true, "dependencies": {"@angular-architects/module-federation": "^16.0.4", "@angular/animations": "^16.2.11", "@angular/cdk": "^16.2.11", "@angular/common": "^16.2.11", "@angular/compiler": "^16.2.11", "@angular/core": "^16.2.11", "@angular/fire": "^7.6.1", "@angular/forms": "^16.2.11", "@angular/material": "^16.2.10", "@angular/platform-browser": "^16.2.11", "@angular/platform-browser-dynamic": "^16.2.11", "@angular/router": "^16.2.11", "@capacitor/camera": "^6.0.2", "@capacitor/core": "^6.1.2", "@davita/bridge-library": "16.2.3-beta.13", "@davita/bridge-utility-library": "16.2.2-beta.9", "@openid/appauth": "^1.3.1", "@stomp/stompjs": "^7.0.0", "husky": "^8.0.3", "jwt-decode": "^4.0.0", "mixpanel-browser": "^2.45.0", "rxfire": "6.0.4", "rxjs": "^7.8.1", "sockjs-client": "^1.6.1", "tslib": "^2.6.2", "uuid": "^9.0.1", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.0", "@angular-eslint/builder": "^16.2.0", "@angular-eslint/eslint-plugin": "^16.2.0", "@angular-eslint/eslint-plugin-template": "^16.2.0", "@angular-eslint/schematics": "^16.2.0", "@angular-eslint/template-parser": "^16.2.0", "@angular/cli": "^16.2.0", "@angular/compiler-cli": "^16.2.0", "@angular/localize": "^16.2.0", "@types/jasmine": "~4.3.0", "@types/mixpanel-browser": "^2.38.0", "@types/sockjs-client": "^1.5.4", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.7.5", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ngx-build-plus": "^16.0.0", "prettier": "^3.3.1", "pretty-quick": "^4.0.0", "typescript": "~5.1.3"}}