export interface ProviderInfo {
  Username?: string;
  Facility?: Facility;
  FirstName?: string;
  LastName?: string;
}

export interface Facility {
  FacilityId?: string;
  Name?: string;
}

export interface Patient {
  firstName: string;
  lastName: string;
  fullName: string;
  masterPatientIdentifier: string;
}

export interface PatientListResponse {
  patientList: Patient[];
  status: string;
  errorMessage: string | null;
}
