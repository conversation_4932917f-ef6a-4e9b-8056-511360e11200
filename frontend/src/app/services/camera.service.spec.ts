import { TestBed } from '@angular/core/testing';
import { CameraService } from './camera.service';

describe('CameraService', () => {
  let service: CameraService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(CameraService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should detect native platform correctly', () => {
    // This will return false in test environment (web)
    expect(service.isNativePlatform()).toBeFalsy();
  });

  // Note: Camera functionality tests are skipped in web environment
  // as Capacitor Camera API is only available on mobile devices
});
