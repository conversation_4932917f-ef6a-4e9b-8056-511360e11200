import { Injectable } from '@angular/core';
import { Observable, catchError, map, tap, throwError } from 'rxjs';
import { HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Thread, Message, MessagingUser, MessagingPatient } from '../models';
import { HttpMessagingService } from './http-messaging.service';
import { Capacitor } from '@capacitor/core';
import { MessagingStateService } from './messaging-state.service';

@Injectable({
  providedIn: 'root',
})
export class HttpPatientMessagingService extends HttpMessagingService {
  constructor(private messageState: MessagingStateService) {
    super();
  }

  getUserThreads(): Observable<Thread[]> {
    if (Capacitor.isNativePlatform()) {
      // Websockets aren't enabled for native, refesh messages to ensure threads are up to date
      this.messageState.resetMessages();
    }
    return this.http.get<Thread[]>(`${this.rootUrl}/threads/user`).pipe(
      tap((threads) => {
        this.isThreadsEmpty = !threads || threads.length === 0;
      }),
      catchError((error: Error) => {
        console.error('ERROR fetching user threads:', error);
        return throwError(error);
      }),
    );
  }

  getPaginatedMessagesByThreadId(
    threadId: string,
    page = 0,
    messagePerPage = this.messagesPerPage,
  ): Observable<Message[]> {
    return this.http
      .get<
        Message[]
      >(`${this.rootUrl}/messages/thread/${threadId}?pageSize=${messagePerPage}&pageNumber=${page}`)
      .pipe(
        catchError((error: Error) => {
          console.error(
            'ERROR fetching paginated messages by thread id:',
            error,
          );
          return throwError(error);
        }),
      );
  }

  updateThreadUnreadMessages(
    threadId: string,
    unreadMessages: number = 0,
  ): Observable<number> {
    return this.http
      .patch<number>(
        `${this.rootUrl}/messages/patch/${threadId}`,
        unreadMessages,
      )
      .pipe(
        catchError((error: Error) => {
          console.error('ERROR update unread Message Status on Thread:', error);
          return throwError(error);
        }),
      );
  }

  postNewThreadPlusFirstMessage(
    threadSubject: string,
    firstMessage: string,
    files: File[] = [],
    mpi?: string,
  ): Observable<Message> {
    const formData = new FormData();

    const messageData = {
      messageText: firstMessage,
      subject: threadSubject,
      participants: [],
    };

    const jsonBlob = new Blob([JSON.stringify(messageData)], {
      type: 'application/json',
    });
    formData.append('data', jsonBlob);

    files.forEach((file) => {
      formData.append('files', file);
    });

    return this.http
      .post<Message>(`${this.rootUrl}/messages/new`, formData)
      .pipe(
        tap(() => {
          this.isThreadsEmpty = false;
        }),
        catchError((error: HttpErrorResponse) => {
          console.error('ERROR posting new subject and first message:', error);
          console.log(error.error);
          return throwError(error);
        }),
      );
  }

  postMessagePlusFileByThreadId(
    newMessage: string,
    threadId: string,
    files: File[] = [],
  ): Observable<Message> {
    const formData = new FormData();

    formData.append('message', newMessage);
    files.forEach((file) => {
      formData.append('files', file);
    });

    return this.http
      .post<Message>(`${this.rootUrl}/messages/new/${threadId}`, formData)
      .pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('ERROR posting message:', error);
          console.log(error.error);
          return throwError(error);
        }),
      );
  }

  getImage(blobId: string, messageId: string): Observable<string> {
    return this.http
      .get(
        `${this.rootUrl}/messages/file?blobId=${blobId}&messageId=${messageId}`,
        {
          responseType: 'blob',
        },
      )
      .pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('ERROR getting image:', error);
          console.log(error.error);
          return throwError(error);
        }),
        map((blob) => {
          const url = URL.createObjectURL(blob);
          return url;
        }),
      );
  }

  createUserIfNotExists(): Observable<MessagingUser> {
    return this.http.post<MessagingUser>(`${this.rootUrl}/users/`, {}).pipe(
      catchError((error: Error) => {
        console.error('ERROR creating user:', error);
        return throwError(() => error);
      }),
    );
  }

  getWsCookie(): Observable<any> {
    const headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(`${this.rootUrl}/authentication/cookie`, {
      headers,
      withCredentials: true,
    });
  }

  getAllPatientsForFacility(): Observable<MessagingPatient[]> {
    throw new Error('Method not implemented.');
  }

  getPatientStatus(mpi: string): Observable<boolean> {
    throw new Error('Method not implemented.');
  }
}
