import { Injectable } from '@angular/core';
import { Observable, catchError, map, retry, tap, throwError } from 'rxjs';
import { HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import {
  Thread,
  Message,
  MessagingUser,
  MessagingPatient,
  PatientListRequest,
  PatientListResponse,
  PatientStatusResponse,
} from '../models';
import { HttpMessagingService } from './http-messaging.service';
import { ProviderInfo } from '../models/teammate';
import { Facility, oinDetails } from '@davita/bridge-utility-library/shared';
import { MessagingStateService } from './messaging-state.service';

@Injectable({
  providedIn: 'root',
})
export class HttpTeammateMessagingService extends HttpMessagingService {
  public teammateUser?: oinDetails;
  public teammateFacility?: Facility | null;
  constructor(private messageState: MessagingStateService) {
    super();
  }

  getUserThreads(): Observable<Thread[]> {
    const headers = this.constructHeaders();
    // Websockets aren't functional for teammate, refesh messages to ensure threads are up to date
    this.messageState.resetMessages();
    return this.http
      .get<Thread[]>(`${this.rootUrl}/provider/threads/user`, { headers })
      .pipe(
        tap((threads) => {
          this.isThreadsEmpty = !threads || threads.length === 0;
        }),
        catchError((error: Error) => {
          console.error('ERROR fetching user threads:', error);
          return throwError(error);
        }),
      );
  }

  getPaginatedMessagesByThreadId(
    threadId: string,
    page = 0,
    messagePerPage = this.messagesPerPage,
  ): Observable<Message[]> {
    // TODO: This is returning messages in the wrong order
    const headers = this.constructHeaders();
    return this.http
      .get<
        Message[]
      >(`${this.rootUrl}/provider/messages/thread/${threadId}?pageSize=${messagePerPage}&pageNumber=${page}`, { headers })
      .pipe(
        catchError((error: Error) => {
          console.error(
            'ERROR fetching paginated messages by thread id:',
            error,
          );
          return throwError(error);
        }),
      );
  }

  updateThreadUnreadMessages(
    threadId: string,
    unreadMessages: number = 0,
  ): Observable<number> {
    const headers = this.constructHeaders();
    return this.http
      .patch<number>(
        `${this.rootUrl}/provider/messages/${threadId}/update-unread`,
        unreadMessages,
        { headers },
      )
      .pipe(
        catchError((error: Error) => {
          console.error('ERROR update unread Message Status on Thread:', error);
          return throwError(error);
        }),
      );
  }

  postNewThreadPlusFirstMessage(
    threadSubject: string,
    firstMessage: string,
    files: File[] = [],
    mpi: string,
  ): Observable<Message> {
    // TODO: Implement ProviderNewMessageDTO
    const formData = new FormData();

    const messageData = {
      messageText: firstMessage,
      subject: threadSubject,
      participants: [mpi],
    };

    const jsonBlob = new Blob([JSON.stringify(messageData)], {
      type: 'application/json',
    });
    formData.append('data', jsonBlob);

    files.forEach((file) => {
      formData.append('files', file);
    });

    const headers = this.constructHeaders();
    return this.http
      .post<Message>(`${this.rootUrl}/provider/messages/new`, formData, {
        headers,
      })
      .pipe(
        tap(() => {
          this.isThreadsEmpty = false;
        }),
        catchError((error: HttpErrorResponse) => {
          console.error('ERROR posting new subject and first message:', error);
          console.log(error.error);
          return throwError(error);
        }),
      );
  }

  postMessagePlusFileByThreadId(
    newMessage: string,
    threadId: string,
    files: File[] = [],
  ): Observable<Message> {
    const formData = new FormData();

    formData.append('message', newMessage);
    files.forEach((file) => {
      formData.append('files', file);
    });
    const headers = this.constructHeaders();
    return this.http
      .post<Message>(
        `${this.rootUrl}/provider/messages/new/${threadId}`,
        formData,
        { headers },
      )
      .pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('ERROR posting message:', error);
          console.log(error.error);
          return throwError(error);
        }),
      );
  }

  getImage(blobId: string, messageId: string): Observable<string> {
    const headers = this.constructHeaders();
    return this.http
      .get(
        `${this.rootUrl}/provider/messages/file?blobId=${blobId}&messageId=${messageId}`,
        {
          responseType: 'blob',
          headers,
        },
      )
      .pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('ERROR getting image:', error);
          console.log(error.error);
          return throwError(error);
        }),
        map((blob) => {
          const url = URL.createObjectURL(blob);
          return url;
        }),
      );
  }

  createUserIfNotExists(): Observable<MessagingUser> {
    const headers = this.constructHeaders();
    return this.http
      .post<MessagingUser>(`${this.rootUrl}/users/`, {}, { headers })
      .pipe(
        catchError((error: Error) => {
          console.error('ERROR creating user:', error);
          return throwError(() => error);
        }),
      );
  }

  getWsCookie(): Observable<any> {
    const headers = this.constructHeaders({
      'Content-Type': 'application/json',
    });
    return this.http.get(`${this.rootUrl}/authentication/cookie`, {
      headers,
      withCredentials: true,
    });
  }

  getAllPatientsForFacility(): Observable<MessagingPatient[]> {
    const headers = this.constructHeaders();
    const patientListRequest: PatientListRequest = {
      facilityNo: this.teammateFacility?.facilityId,
    };

    return this.http
      .post<PatientListResponse>(
        `${this.rootUrl}/users/patient/list/facility`,
        patientListRequest,
        { headers },
      )
      .pipe(
        map((response) => {
          if (response.status === 'Error') {
            console.error(response.errorMessage);
            throw new Error(
              response.errorMessage ||
                `Error getting patient list for facility ${this.teammateFacility?.facilityId}`,
            );
          }
          return response.patientList;
        }),
        retry(3),
      );
  }

  public getPatientStatus(mpi: string): Observable<boolean> {
    const headers = this.constructHeaders();
    const userRequest = {
      mpi: mpi,
    };
    return this.http
      .post<PatientStatusResponse>(
        `${this.rootUrl}/users/patientstatus`,
        userRequest,
        { headers },
      )
      .pipe(
        map((response) => {
          if (response.status === 'Error') {
            console.error(response.errorMessage);
            throw new Error(
              response.errorMessage || `Error validating patient's status`,
            );
          }
          return response.active;
        }),
        retry(3),
      );
  }

  private constructHeaders(extra?: { [key: string]: string }): HttpHeaders {
    return new HttpHeaders({ ...this.createProviderHeader(), ...extra });
  }

  private createProviderHeader(): { [key: string]: string } {
    const providerInfo: ProviderInfo = {
      Username: this.teammateUser?.userLogin,
      FirstName: this.teammateUser?.firstName,
      LastName: this.teammateUser?.lastName,
      Facility: {
        Name: this.teammateFacility?.facilityName,
        FacilityId: this.teammateFacility?.facilityId,
      },
    };
    return {
      'Provider-Info': JSON.stringify(providerInfo),
    };
  }
}
