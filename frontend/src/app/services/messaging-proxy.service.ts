import { inject, Injectable } from '@angular/core';
import { HttpPatientMessagingService } from './http-patient-messaging.service';
import { HttpTeammateMessagingService } from './http-teammate-messaging.service';
import { BriSessionStorageService } from '@davita/bridge-utility-library/session-storage';
import { HttpMessagingService } from './http-messaging.service';
import { MessagingUser } from '../models';

@Injectable({
  providedIn: 'root',
})
export class MessagingProxyService {
  private httpMessagingService!:
    | HttpPatientMessagingService
    | HttpTeammateMessagingService;

  constructor(private briSessionStorage: BriSessionStorageService) {}

  getHttpService(): HttpMessagingService {
    return this.httpMessagingService;
  }

  public isUser(savedUser: MessagingUser, messageUser: MessagingUser) {
    if (this.briSessionStorage.getItem('login_type') === 'teammate') {
      return (
        savedUser.adUsername?.toLowerCase() ==
        messageUser.adUsername?.toLowerCase()
      );
    } else {
      return savedUser.externalId == messageUser.externalId;
    }
  }

  public setupHttpService() {
    if (this.briSessionStorage.getItem('login_type') === 'teammate') {
      this.httpMessagingService = inject(HttpTeammateMessagingService);
    } else {
      this.httpMessagingService = inject(HttpPatientMessagingService);
    }
  }
}
