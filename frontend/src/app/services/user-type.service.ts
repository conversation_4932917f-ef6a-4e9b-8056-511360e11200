import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { UserType } from '../models/roles';

@Injectable({
  providedIn: 'root',
})
export class UserTypeService {
  private userTypeSubject: BehaviorSubject<UserType | null>;
  public userType$: Observable<UserType | null>;
  constructor() {
    this.userTypeSubject = new BehaviorSubject<UserType | null>(null);
    this.userType$ = this.userTypeSubject.asObservable();
  }

  setUserType(type: UserType) {
    this.userTypeSubject.next(type);
  }
}
