.image-container {
  position: fixed; /* Use fixed positioning */
  top: 0;
  left: 0;
  width: 100vw; /* Viewport width */
  height: 100vh; /* Viewport height */
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: black; /* Or any color */
}

.centered-image {
  max-width: 100%;
  max-height: 100%;
}

.close-button {
  position: absolute;
  top: 0;
  right: 0;
  margin-right: 10px;
  margin-top: env(safe-area-inset-top, 0);
  color: white;
}

:host bri-buttons {
  ::ng-deep .mdc-button.mdc-button--unelevated {
    background-color: transparent;
    border: none;
  }
}
