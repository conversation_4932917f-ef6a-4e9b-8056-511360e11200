import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { MockHttpMessagingService } from 'src/app/messaging/mocks/services';
import { ImageModalComponent } from './image-modal.component';
import { HttpClient, HttpHandler } from '@angular/common/http';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { mockDialogData } from 'src/app/messaging/mocks/data';
import { MatIconModule } from '@angular/material/icon';

describe('ImageComponent', () => {
  let component: ImageModalComponent;
  let fixture: ComponentFixture<ImageModalComponent>;
  // Create a mock for MatDialogRef
  const mockMatDialogRef = {
    close: jasmine.createSpy('close'),
  };
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MatDialogModule, MatIconModule],
      declarations: [ImageModalComponent],
      providers: [
        {
          provide: HttpMessagingService,
          useValue: new MockHttpMessagingService(),
        },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },
        { provide: MatDialogRef, useValue: mockMatDialogRef },
        HttpClient,
        HttpHandler,
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(ImageModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should contain image', () => {
    const compiled = fixture.debugElement.nativeElement;
    const image = compiled.querySelector('img');
    expect(image.src).toContain('MOCK-IMAGE-DATA');
  });

  it('should contain correct image tag', () => {
    const compiled = fixture.debugElement.nativeElement;
    const image = compiled.querySelector('img');
    expect(image.alt).toContain('user submitted image');
  });

  it('should contain close button', () => {
    const buttons: HTMLButtonElement[] =
      fixture.nativeElement.querySelectorAll('button');
    const closeButton = Array.from(buttons).find(
      (button: HTMLButtonElement) => button?.textContent?.trim() === 'close',
    );
    expect(closeButton).toBeTruthy();
  });
});
