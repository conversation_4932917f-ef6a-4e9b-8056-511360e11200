<ng-container *ngIf="imageStatus === Status.success; else elseShowSkeleton">
  <img
    id="image"
    [src]="image"
    alt="blobId"
    class="user-image"
    (click)="toggleImageModal(image)"
  />
</ng-container>
<ng-template #elseShowSkeleton>
  <ng-container *ngIf="imageStatus === Status.loading; else elseShowError">
    <img
      class="skeleton-loader"
      [src]="assetsUrl + '/images/loading-skeleton.gif'"
      alt="loading skeleton"
    />
  </ng-container>
  <ng-template #elseShowError>
    <div (click)="loadImage()" class="error-image" alt="error image">
      <bri-icon [size]="36" [iconName]="'di di-media80'"></bri-icon>
      <p>Tap to Load</p>
    </div>
  </ng-template>
</ng-template>
