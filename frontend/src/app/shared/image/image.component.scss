@import "@davita/bridge-library/scss/colors";

.user-image {
  width: auto;
  border-radius: 8px;
  height: 150px;
  object-fit: contain;
  padding-bottom: 5px;
  padding-top: 5px;
}

.skeleton-loader {
  width: 200px;
  height: 150px;
}

.error-image {
  width: 200px;
  height: 150px;
  background-color: $light-color-ui-separator;
  border-radius: 15px; /* Adjust this value for the desired roundness */
  overflow: hidden; /* Ensure the image fits within the rounded corners */
  display: flex;
  flex-direction: column;

  align-items: center;
  justify-content: center;
}
