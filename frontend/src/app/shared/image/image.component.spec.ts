import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { MockHttpMessagingService } from 'src/app/messaging/mocks/services';
import { ImageComponent } from './image.component';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';

describe('ImageComponent', () => {
  let component: ImageComponent;
  let fixture: ComponentFixture<ImageComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MatDialogModule],
      declarations: [ImageComponent],
      providers: [
        {
          provide: HttpMessagingService,
          useValue: new MockHttpMessagingService(),
        },
        MatDialog,
        HttpClient,
        HttpHandler,
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(ImageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should contain image', () => {
    const compiled = fixture.debugElement.nativeElement;
    const image = compiled.querySelector('#image');
    expect(image.src).toContain('MOCK-IMAGE');
  });
});
