import { Component, Input, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Observable, iif, mergeMap, of } from 'rxjs';
import { Status } from 'src/app/models';
import { ImageStateService } from 'src/app/services/image-state.service';
import { MessagingProxyService } from 'src/app/services/messaging-proxy.service';
import { ImageModalComponent } from 'src/app/shared/image-modal/image-modal.component';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-image',
  templateUrl: './image.component.html',
  styleUrls: ['./image.component.scss'],
})
export class ImageComponent implements OnInit {
  @Input() blobId = '';
  @Input() messageId = '';

  // if have image Object URL pass it here, this will override blobId and messageId
  @Input() imageObjectUrl: string | undefined = undefined;
  image = '';
  imageStatus: Status = Status.idle;
  assetsUrl = environment.assetsUrl;
  Status = Status;

  constructor(
    private imageState: ImageStateService,
    private proxyService: MessagingProxyService,
    private dialog: MatDialog,
  ) { }

  ngOnInit(): void {
    this.loadImage();
  }

  loadImage(): void {
    this.imageStatus = Status.loading;

    if (this.imageObjectUrl) {
      this.image = this.imageObjectUrl;
      this.imageStatus = Status.success;
    } else {
      this.imageState.images$
        .pipe(
          mergeMap((images) => {
            // Check if 'images' and 'images[blobId]' exist
            let imageExists = false;
            if (images && images[this.blobId]) imageExists = true;

            // if image exists, return it
            // Otherwise, fetch image using blob and message id
            return iif(
              () => imageExists,
              of(images[this.blobId]),
              this.fetchImageById(this.blobId, this.messageId),
            );
          }),
        )
        .subscribe({
          next: (image) => {
            this.image = image;
            this.imageStatus = Status.success;
          },
          error: (err) => {
            console.error(err);
            this.imageStatus = Status.error;
          },
        });
    }
  }

  fetchImageById(blobId: string, messageId: string): Observable<string> {
    return this.proxyService
      .getHttpService()
      .getImage(blobId, messageId)
      .pipe(
        mergeMap((image) => {
          // cache image
          this.imageState.setImage(blobId, image);
          return of(image);
        }),
      );
  }

  toggleImageModal(imgSrc: string | ArrayBuffer | undefined) {
    if (!imgSrc) return;
    // open modal
    this.dialog.open(ImageModalComponent, {
      width: '100%',
      height: '100%',
      data: { imgSrc },
    });
  }
}
