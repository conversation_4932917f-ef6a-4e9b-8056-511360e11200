@import "@davita/bridge-library/scss/colors";

.landing-page {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-width: 482px;
}
.starting-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
  height: 99%; //scroll bar shows if 100%
  padding: 0 16px;
  bri-empty-state ::ng-deep .empty-state-container {
    background-color: unset !important;
    position: relative !important;
  }
  bri-empty-state ::ng-deep .empty-state-icon {
    color: $light-color-blue;
    @media (prefers-color-scheme: dark) {
      color: $dark-color-light-blue;
    }
  }
}

@media screen and (max-width: 959px) {
  .starting-content p {
    max-width: 640px;
    padding: 0 16px;
  }
}

@media screen and (max-width: 599px) {
  .starting-content p {
    width: auto;
    max-width: none;
    padding: 0 16px;
  }
}

@media screen and (max-width: 482px) {
  .landing-page {
    min-width: 0;
  }
}
