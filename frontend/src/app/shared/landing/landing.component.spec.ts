import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { MockHttpMessagingService } from 'src/app/messaging/mocks/services';
import { LandingComponent } from './landing.component';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { BriPatientAuthorizationService } from '@davita/bridge-utility-library/authorization';
import { MockBriPatientAuthorizationService } from 'src/app/messaging/mocks/services';

describe('LandingComponent', () => {
  let component: LandingComponent;
  let fixture: ComponentFixture<LandingComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [LandingComponent],
      providers: [
        {
          provide: BriPatientAuthorizationService,
          useClass: MockBriPatientAuthorizationService,
        },
        {
          provide: HttpMessagingService,
          useValue: new MockHttpMessagingService(),
        },
        HttpClient,
        HttpHandler,
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(LandingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should contain welcome message', () => {
    const compiled = fixture.debugElement.nativeElement;
    const welcomeMessage = compiled.querySelector('.starting-content');
    expect(welcomeMessage.textContent).toContain(
      'Welcome to Messages, a place to connect with your care team!',
    );
    expect(welcomeMessage.textContent).toContain(
      'Select the “New Message” button to get started.',
    );
  });
});
