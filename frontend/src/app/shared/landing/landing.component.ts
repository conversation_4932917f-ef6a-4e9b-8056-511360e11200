import { Component, HostListener, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DeviceStateService } from 'src/app/services/device-state.service';
import { MessagingStateService } from 'src/app/services/messaging-state.service';

@Component({
  selector: 'app-landing',
  templateUrl: './landing.component.html',
  styleUrls: ['./landing.component.scss'],
})
export class LandingComponent implements OnInit {
  deviceType: string = this.deviceState.determineDevice();
  emptyThread: boolean = false;
  presentThreadsDescription: string =
    'Select "New Message" or an existing message thread.';
  emptyThreadsDescription: string =
    'Select the “New Message” button to get started.';
  presentHeaderText: string = 'Connect with your care team here.';
  emptyHeaderText: string = 'Welcome to Messages, a place to connect with your care team!';

  constructor(
    private deviceState: DeviceStateService,
    private messagingState: MessagingStateService,
    private router: Router,
  ) {}

  @HostListener('window:resize')
  onResize(): void {
    this.deviceType = this.deviceState.determineDevice();
  }

  ngOnInit(): void {
    this.deviceState.threadListVisible = true;
    this.messagingState.threads$.subscribe((threads) => {
      this.emptyThread = !threads || threads.length === 0;
    });
    const currentURL = this.router.url;
    if (currentURL.includes('teammate-messaging')) {
      this.presentHeaderText = 'Connect with your patient here.';
      this.emptyHeaderText = 'Welcome to Messages, a place to connect with your patient!';
    } else {
      this.presentHeaderText = 'Connect with your care team here.';
      this.emptyHeaderText = 'Welcome to Messages, a place to connect with your care team!';
    }
  }
}
