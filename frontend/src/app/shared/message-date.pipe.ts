import { Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';

@Pipe({
  name: 'messageDate',
})
export class MessageDatePipe implements PipeTransform {
  constructor(private datePipe: DatePipe) {}

  transform(dateString: string): string {
    const date = new Date(dateString);
    const currentDay = this.normalizeDate(date);

    if (currentDay.getTime() === this.today().getTime()) {
      return `Today`;
    } else if (currentDay.getTime() === this.yesterday().getTime()) {
      return `Yesterday`;
    } else if (currentDay.getTime() < this.oneWeekAgo().getTime()) {
      return this.datePipe.transform(date, 'MM/dd') || '';
    } else {
      return this.datePipe.transform(date, 'EEEE') || '';
    }
  }

  today(): Date {
    const today = new Date();
    return this.normalizeDate(today);
  }

  yesterday(): Date {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return this.normalizeDate(yesterday);
  }

  oneWeekAgo(): Date {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 7);
    return this.normalizeDate(yesterday);
  }

  // set time to 12am so we are only comparing the day
  normalizeDate(date: Date) {
    const _date = new Date(date.getTime());
    _date.setHours(0, 0, 0, 0);
    return _date;
  }
}
