<div>
  <div
    class="message-title-container"
    *ngIf="!readOnly && deviceType === 'desktop'"
  >
    <bri-buttons
      briBasic
      [btnText]="'Cancel'"
      [className]="'cancel-button'"
      (buttonClicked)="
        hasUserInputedText ? openCancelModal() : redirectUserToLandingPage()
      "
    ></bri-buttons>
    <div class="center">
      <p *ngIf="subject">{{ subject }}</p>
      <p *ngIf="!subject">New Message</p>
    </div>
  </div>
  <div
    class="message-title-container"
    *ngIf="!readOnly && deviceType !== 'desktop'"
  >
    <bri-sub-header-v2
      [sectionHeader]="'New Message'"
      [leftContent]="{ showBackButton: true, backText: 'Cancel' }"
      (backBtnClicked)="
        hasUserInputedText ? openCancelModal() : redirectUserToLandingPage()
      "
      [hideBackIcon]="true"
    />
  </div>
  <p class="message-title mat-headline-6" *ngIf="readOnly">
    {{ subject }}
  </p>
  <mat-divider *ngIf="deviceType === 'desktop'" />
  <div class="subject-container" *ngIf="!readOnly">
    <p class="mat-subtitle-1">Subject:</p>
    <bri-text-fields
      [ngStyle]="{ width: '100%' }"
      id="subject"
      [label]="'subjectInput'"
      [errorText]="'Please type a subject'"
      [textFormControl]="subjectFormControl"
      [hideOutlineLabel]="true"
      [placeHolder]="'Type Subject'"
      [requiredFieldErrorText]="'Subject is Required'"
      [maxLength]="140"
    ></bri-text-fields>
  </div>
</div>

<!-- Missing Subject or Message Modal -->
<bri-alert
  (primaryButtonClick)="redirectUserToLandingPage()"
  (secondaryButtonClick)="closeModal()"
  [warnColorPrimary]="true"
/>
