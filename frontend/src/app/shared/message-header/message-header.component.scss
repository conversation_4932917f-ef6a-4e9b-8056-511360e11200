@import "@davita/bridge-library/scss/colors";

.message-title-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-right: 105px;
  .center {
    flex-grow: 1;
    text-align: center;
  }
  .cancel-button {
    margin-left: 8px;
  }
}
.message-title {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}

.subject-container {
  display: flex;
  flex-direction: row;
  align-items: top;
  padding: 16px 16px 8px 0;
  width: 100%;
  p {
    padding: 8px 8px 0 16px;
  }
}
::ng-deep {
  app-optimized-fields {
    .checkbox-form {
      display: flex;
      margin: 16px 0 24px;
      flex-direction: row;
      gap: 24px;
    }
    .mat-mdc-card-outlined {
      background-color: $light-color-ui-background !important;
      @media (prefers-color-scheme: dark) {
        background-color: $dark-color-ui-background !important;
      }
    }
    .beta-note {
      margin: 0 0 16px 0 !important;
    }
    .mat-mdc-tab-body-wrapper {
      padding: 20px 0;
    }
    .mat-mdc-card {
      padding: 20px;
    }
  }
}
bri-sub-header-v2 ::ng-deep div.header-container {
  border-bottom: 1px solid $light-color-ui-disabled;
  @media (prefers-color-scheme: dark) {
    border-bottom: 1px solid $dark-color-ui-disabled;
  }
}
mat-divider {
  background-color: $light-color-ui-separator;
  border-top: 1px solid $light-color-ui-separator;
  @media (prefers-color-scheme: dark) {
    background-color: $dark-color-ui-disabled;
    border-top: 1px solid $dark-color-ui-disabled;
  }
}
bri-text-fields
  ::ng-deep
  div
  mat-form-field
  div
  div
  div
  .mat-subtitle-1::placeholder {
  color: $light-color-text-50 !important;
}

@media screen and (max-width: 959px) {
  .subject-container {
    padding: 24px 16px 8px;
  }

  .message-title-container {
    bri-sub-header-v2 {
      ::ng-deep .header-container {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        z-index: 999;
        margin-top: env(safe-area-inset-top, 0);
      }
    }
  }
}

@media screen and (max-width: 599px) {
  .subject-container {
    padding: 8px 16px;
    p {
      padding: 8px 8px 8px 0;
    }
  }
}
