import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { MockHttpMessagingService } from 'src/app/messaging/mocks/services';
import { MessageHeaderComponent } from './message-header.component';
import { BriAlertModule } from '@davita/bridge-library/alert';
import { BriButtonsModule } from '@davita/bridge-library/buttons';
import { MatDividerModule } from '@angular/material/divider';
import { BriTextFieldsModule } from '@davita/bridge-library/fields/text';
import {
  BrowserAnimationsModule,
  NoopAnimationsModule,
} from '@angular/platform-browser/animations';
import { BriSubHeaderModule } from '@davita/bridge-library/sub-header';
import { By } from '@angular/platform-browser';

describe('MessageHeaderComponent', () => {
  let component: MessageHeaderComponent;
  let fixture: ComponentFixture<MessageHeaderComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        BriAlertModule,
        BriButtonsModule,
        MatDividerModule,
        BriTextFieldsModule,
        BrowserAnimationsModule,
        NoopAnimationsModule,
        BriSubHeaderModule,
      ],
      declarations: [MessageHeaderComponent],
      providers: [
        {
          provide: HttpMessagingService,
          useValue: new MockHttpMessagingService(),
        },
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(MessageHeaderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should contain Subject:', () => {
    const compiled = fixture.debugElement.nativeElement;
    const inputTitle = compiled.querySelector('.mat-subtitle-1');
    expect(inputTitle.textContent).toContain('Subject:');
  });

  it('contains input box for subject', () => {
    const inputElement =
      fixture.debugElement.nativeElement.querySelector('#subject');
    expect(inputElement).toBeTruthy();
  });

  it('contains cancel button', () => {
    const buttons = fixture.debugElement.queryAll(By.css('button'));
    const cancelButton = Array.from(buttons).find(
      (button) => button.nativeElement.textContent?.trim() === 'Cancel',
    );
    expect(cancelButton).toBeTruthy();
  });

  it('should contain New Message heading', () => {
    const compiled = fixture.debugElement.nativeElement;
    const inputTitle = compiled.querySelector('.message-title-container');
    expect(inputTitle.textContent).toContain('New Message');
  });

  it('modal appears when user clicks cancel button', () => {
    const buttons = fixture.debugElement.queryAll(By.css('button'));
    const cancelButton = Array.from(buttons).find(
      (button) => button.nativeElement.textContent?.trim() === 'Cancel',
    );

    cancelButton?.triggerEventHandler('click', null);

    fixture.detectChanges();
    const modalElement = document.querySelector('.cdk-overlay-container');
    expect(modalElement).toBeTruthy();
    expect(modalElement?.textContent).toContain('Cancel your new message?');
  });

  it('cancel modal contains correct headline', () => {
    const buttons = fixture.debugElement.queryAll(By.css('button'));
    const cancelButton = Array.from(buttons).find(
      (button) => button.nativeElement.textContent?.trim() === 'Cancel',
    );

    cancelButton?.triggerEventHandler('click', null);

    fixture.detectChanges();
    const modalElement = document.querySelector('.cdk-overlay-container');
    expect(modalElement?.textContent).toContain('Cancel your new message?');
  });
});
