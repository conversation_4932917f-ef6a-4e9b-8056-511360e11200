import {
  Component,
  EventEmitter,
  HostListener,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { BriAlertComponent, DialogData } from '@davita/bridge-library/alert';
import { CapcitorAppRefreshService } from '@davita/bridge-library/capacitor-app-refresh';
import { DeviceStateService } from 'src/app/services/device-state.service';

@Component({
  selector: 'app-message-header',
  templateUrl: './message-header.component.html',
  styleUrls: ['./message-header.component.scss'],
})
export class MessageHeaderComponent {
  constructor(
    private deviceState: DeviceStateService,
    private capacitorAppRefreshService: CapcitorAppRefreshService,
  ) {}

  @ViewChild(BriAlertComponent, { static: false })
  private component!: BriAlertComponent;
  @Input() readOnly: boolean = false;
  @Input() subject: string = '';
  @Input() subjectFormControl: FormControl = new FormControl('');
  @Input() hasUserInputedText = false;
  @Output() newMessageClosed: EventEmitter<void> = new EventEmitter();

  deviceType: string = this.deviceState.determineDevice();
  private isAlertOpen = false;

  redirectUserToLandingPage() {
    this.capacitorAppRefreshService.setlockCapacitorAppRefresh(false);
    this.newMessageClosed.emit();
    this.deviceState.threadListVisible = true;
    if (this.isAlertOpen) {
      this.isAlertOpen = false;
      this.component.close();
    }
  }

  openCancelModal() {
    this.isAlertOpen = true;
    const data: DialogData = {
      header: 'Cancel your new message?',
      description:
        'Are you sure you want to cancel? Your unsent message will not send or be saved.',
      primaryButtonLabel: 'Yes, Cancel',
      secondaryButtonLabel: 'No',
    };
    this.component.open(data);
  }

  closeModal() {
    this.isAlertOpen = false;
    this.component.close();
  }

  @HostListener('window:resize')
  onResize(): void {
    this.deviceType = this.deviceState.determineDevice();
  }
}
