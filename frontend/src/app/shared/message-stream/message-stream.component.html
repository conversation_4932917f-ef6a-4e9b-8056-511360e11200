<ng-container *ngIf="isLoaded.messages; else loadingState">
  <ng-container *ngFor="let message of messages; let i = index">
    <p *ngIf="showNewDayDateDivider(i)" class="header-time">
      {{ message.sentAt | messageDate }}
    </p>
    <ng-container
      *ngIf="
        !message.fileUploads?.length &&
          !message.undeliveredImage?.imageObjectUrl;
        else showImage
      "
    >
      <bri-chat-message
        [outgoing]="isUser(message.sender)"
        [name]="
          !isUser(message.sender)
            ? showMessageSender(i)
              ? message.sender.firstName
              : null
            : showMessageSender(i)
              ? 'You'
              : null
        "
        [roleLabel]="getRoleLabel(message.sender, i)"
        [roleColor]="careCoordinatorChipColor"
        [roleTextColor]="whiteText"
        [messageText]="message.messageText"
        [dateTime]="
          showMessageDate(i) ? (message.sentAt | date: 'hh:mm a') : null
        "
        [delivered]="message.deliveryStatus !== DeliveryStatus.undelivered"
        (iconClicked)="
          message.deliveryStatus === DeliveryStatus.undelivered &&
            onRetryButtonClicked()
        "
      ></bri-chat-message>
    </ng-container>
    <ng-template #showImage>
      <div [ngClass]="{ 'outgoing-style': isUser(message.sender) }">
        <div class="role">
          <ng-container *ngIf="!isUser(message.sender); else elseBlock">
            <ng-container *ngIf="showMessageSender(i)">
              <p>{{ message.sender.firstName }}</p>
              <bri-chips
                [chipColor]="careCoordinatorChipColor"
                [textColor]="whiteText"
                [labelText]="getRoleLabel(message.sender, i)"
              ></bri-chips>
            </ng-container>
          </ng-container>
          <ng-template #elseBlock>
            <ng-container *ngIf="showMessageSender(i)">
              <p>You</p>
            </ng-container>
          </ng-template>
        </div>
        <ng-container
          *ngIf="
            !message.undeliveredImage?.imageObjectUrl;
            else elseShowUndeliveredImage
          "
        >
          <app-image
            [blobId]="message.fileUploads?.[0]?.gcsBlobId || ''"
            [messageId]="message.id"
            [imageObjectUrl]="message.undeliveredImage?.imageObjectUrl"
          />
        </ng-container>
        <ng-template #elseShowUndeliveredImage>
          <div class="error">
          <app-image
            [blobId]="message.fileUploads?.[0]?.gcsBlobId || ''"
            [messageId]="message.id"
            [imageObjectUrl]="message.undeliveredImage?.imageObjectUrl"
          />
            <ng-container
              *ngIf="message.deliveryStatus === DeliveryStatus.undelivered"
            >
              <span (click)="onRetryButtonClicked()">
                <bri-icon iconName="di-I" [size]="24"></bri-icon>
              </span>
            </ng-container>
          </div>

          <ng-container
            *ngIf="message.deliveryStatus === DeliveryStatus.undelivered"
          >
            <div class="not-delivered">Not Delivered</div>
          </ng-container>
        </ng-template>
        <ng-container *ngIf="showMessageDate(i)">
          <p>{{ message.sentAt | date: "hh:mm a" }}</p>
        </ng-container>
      </div>
    </ng-template>
  </ng-container>
</ng-container>

<!-- Loading Bar  -->
<ng-template #loadingState>
  <div class="loading">
    <bri-progress-bar message="Getting Messages"></bri-progress-bar>
  </div>
</ng-template>
