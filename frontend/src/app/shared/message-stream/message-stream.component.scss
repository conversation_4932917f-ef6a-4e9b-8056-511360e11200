@import "@davita/bridge-library/scss/colors";

p {
  color: $light-color-text-25;
  @media (prefers-color-scheme: dark) {
    color: $dark-color-text-25;
  }
}
.header-time {
  text-align: center;
}
.loading {
  display: flex;
  justify-content: center; /* Aligns horizontally */
  align-items: center; /* Aligns vertically */
  margin-top: 25vh;
}
.link {
  text-align: center;
  color: blue;
  text-decoration: underline;
  font-style: italic;
  cursor: pointer;
  display: flex;
  justify-content: center; /* Horizontal centering */
  align-items: center; /* Vertical centering */
}
::ng-deep .outgoing {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.outgoing-style {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  .role {
    justify-content: flex-end;
  }
  p {
    text-align: right;
  }
}

.header-time {
  text-align: center;
}

.role {
  display: flex;
  flex-direction: row;
  font-size: 16px;
  align-items: center;
  gap: 10px;
  padding-top: 8px;
  p {
    color: $light-color-text-100;
  }
}

.user-image {
  width: auto;
  border-radius: 8px;
  height: 150px;
  object-fit: contain;
  padding-bottom: 5px;
  padding-top: 5px;
}

.error {
  display: flex;
  flex-direction: row;
  justify-content: flex-end; /* Aligns horizontally */
  align-items: center; /* Aligns vertically */
  span {
    color: $light-color-red;
    @media (prefers-color-scheme: dark) {
      color: $dark-color-red;
    }
  }
}

.not-delivered {
  color: $light-color-red;
  font-weight: 400;
  text-align: right;
  @media (prefers-color-scheme: dark) {
    color: $dark-color-red;
  }
}
