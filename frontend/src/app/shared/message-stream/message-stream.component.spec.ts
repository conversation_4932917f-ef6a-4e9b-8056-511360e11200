import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ElementRef } from '@angular/core';
import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { MockHttpMessagingService } from '../mocks/services';
import { MessageStreamComponent } from './message-stream.component';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { BriPatientAuthorizationService } from '@davita/bridge-utility-library/authorization';
import { MockBriPatientAuthorizationService } from '../mocks/services';
import { Subject } from 'rxjs';
import { BriProgressBarModule } from '@davita/bridge-library/progress-bar';

describe('MessageStreamComponent', () => {
  let component: MessageStreamComponent;
  let fixture: ComponentFixture<MessageStreamComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BriProgressBarModule],
      declarations: [MessageStreamComponent],
      providers: [
        {
          provide: BriPatientAuthorizationService,
          useValue: new MockBriPatientAuthorizationService(),
        },
        {
          provide: HttpMessagingService,
          useValue: new MockHttpMessagingService(),
        },
        HttpClient,
        HttpHandler,
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(MessageStreamComponent);
    component = fixture.componentInstance;
    // Mock ElementRef
    component.scrollContainer = {
      nativeElement: document.createElement('div'),
    } as ElementRef;
    component.triggerInfiniteScroll$ = new Subject();
    component.id = '351f41bb-811d-492f-a228-6e17f334a1f7';
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should contain progress bar', () => {
    const compiled = fixture.debugElement.nativeElement;
    const title = compiled.querySelector('.loading');
    expect(title.textContent).toContain('Getting Messages');
  });
});
