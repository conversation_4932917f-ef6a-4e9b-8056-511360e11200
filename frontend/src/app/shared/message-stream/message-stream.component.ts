import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  NgZone,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  Message,
  PaginatedMessagesByThread,
  MessagingUser,
  DeliveryStatus,
} from 'src/app/models';
import { MessagingStateService } from 'src/app/services/messaging-state.service';
import {
  switchMap,
  iif,
  of,
  Subscription,
  Subject,
  debounceTime,
  filter,
  Observable,
} from 'rxjs';
import { environment } from 'src/environments/environment';
import { MessagingProxyService } from 'src/app/services/messaging-proxy.service';
import { MessagingSenderUserType } from 'src/app/models/roles';

interface Loaded {
  messages: boolean;
}

interface PageStatus {
  currentPage: number;
  onLastPage: boolean;
}

@Component({
  selector: 'app-message-stream',
  templateUrl: './message-stream.component.html',
  styleUrls: ['./message-stream.component.scss'],
})
export class MessageStreamComponent implements OnChanges, OnInit, OnDestroy {
  @Input() id: string = '';
  @Input() scrollContainer!: ElementRef;
  @Input() triggerInfiniteScroll$!: Subject<number>;
  @Output() newPageLoaded = new EventEmitter<number>();
  @Output() newPageLoading = new EventEmitter<boolean>();
  @Output() retryButtonClicked = new EventEmitter();

  constructor(
    private proxyService: MessagingProxyService,
    private messageState: MessagingStateService,
    private zone: NgZone,
  ) { }
  messages: Message[] = [] as Message[];
  private messageSubcription: Subscription = new Subscription();
  private triggerInfiniteScrollSubscription: Subscription = new Subscription();
  private currentScrollHeight = 0;
  private MESSAGES_PER_PAGE = environment.messaging.messages_per_api_request;
  DeliveryStatus = DeliveryStatus; // expose enum

  // pagination state
  pageStatus: PageStatus = {
    currentPage: 0,
    onLastPage: false,
  };

  // loading state for messages
  isLoaded: Loaded = {
    messages: false,
  };

  shouldScrollBottom = false;

  whiteText = '#fff'; //$light-color-ui-background
  darkText = '#063846'; //$light-color-navy
  careCoordinatorChipColor = '#063846'; //$light-color-navy
  registeredNurseChipColor = '#B45D3B'; //$light-color-purple
  nursePractitionerChipColor = '#0C7D83'; //$light-color-blue
  engagementSpecialistChipColor = '#DCB43F'; //$light-color-yellow

  ngOnInit(): void {
    // listen for infinite scroll event
    this.triggerInfiniteScrollSubscription = this.triggerInfiniteScroll$
      .pipe(
        debounceTime(100),
        filter(
          () =>
            !this.pageStatus.onLastPage &&
            this.messages.length >= this.MESSAGES_PER_PAGE,
        ),
      )
      .subscribe(() => {
        console.log('TRIGGER NEW PAGE');
        this.onScrollForOlderMessages();
      });
  }

  ngOnDestroy(): void {
    this.messageSubcription.unsubscribe();
    this.triggerInfiniteScrollSubscription.unsubscribe();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['id']) {
      this.isLoaded.messages = false;
      const idChange = changes['id'];

      // Check if the id has a new value
      if (idChange.currentValue !== idChange.previousValue) {
        const newId = idChange.currentValue;
        this.pageStatus.currentPage = 0;
        this.messages = [] as Message[];
        this.pageStatus.onLastPage = false;

        // Now you have access to the updated id
        // retrieve messages for this thread if
        // if they do not exist make an
        // API call to obtain first page of messages for the new id
        this.shouldScrollBottom = true;
        this.loadMessages(newId);
      }
    }
  }

  // load Intial messages from page 0 and handle updates
  // from new messages posted by user
  loadMessages(threadId: string) {
    this.messageSubcription = this.messageState.messages$
      .pipe(
        // only emit a new value if new page (greater than page 0) from pagination is not loading
        switchMap((messages) => {
          // Check if 'messages' and 'messages[newId]' exist
          let threadMessagesExist = false;
          if (messages && messages[threadId]) threadMessagesExist = true;

          // If they exist, return them wrapped in an Observable
          // Otherwise, fetch new messages for the thread ID
          return iif(
            () => threadMessagesExist,
            of(messages),
            this.fetchMessagesByThreadId(threadId),
          );
        }),
      )
      .subscribe((messages: PaginatedMessagesByThread) => {
        // load new messages
        this.pageStatus.currentPage =
          Object.keys(messages[threadId]).length - 1; // counting # of page
        this.messages = [];
        for (let page = this.pageStatus.currentPage; page >= 0; page--) {
          this.messages.push(...messages[threadId][page]);
        }
        if (this.shouldScrollBottom) {
          this.shouldScrollBottom = false;
          setTimeout(() => {
            this.scrollToBottom();
          });
        }
        this.isLoaded.messages = true;
      });
  }

  loadMessagesOfPage(page: number, threadId: string): void {
    this.newPageLoading.emit(true);
    // fetch new page from API
    this.fetchMessagesByThreadId(threadId, page).subscribe((messages) => {
      if (Object.keys(messages[threadId]).length < page + 1) {
        this.pageStatus.onLastPage = true;
      }
      this.newPageLoaded.emit(this.currentScrollHeight);
      this.newPageLoading.emit(false);
    });
  }

  onRetryButtonClicked() {
    this.retryButtonClicked.emit();
  }

  onScrollForOlderMessages() {
    this.currentScrollHeight = this.scrollContainer.nativeElement.scrollHeight;
    // Save the current scroll height before loading new messages

    this.loadMessagesOfPage(this.pageStatus.currentPage + 1, this.id);
  }

  scrollToBottom() {
    const element = this.scrollContainer.nativeElement;
    element.scrollTop = element.scrollHeight;
  }

  isUser(messageSender: MessagingUser): boolean {
    return this.messageState.isUser(messageSender);
  }

  fetchMessagesByThreadId(
    id: string,
    page = 0,
  ): Observable<PaginatedMessagesByThread> {
    return this.proxyService
      .getHttpService()
      .getPaginatedMessagesByThreadId(id, page)
      .pipe(
        switchMap((messages) =>
          this.messageState.updateMessages(messages, id, page),
        ),
      );
  }

  // show date above message that starts on new day
  showNewDayDateDivider(messageIndex: number): boolean {
    if (messageIndex == 0) return true;

    const currentMessageDate = new Date(this.messages[messageIndex].sentAt);
    const currentMessageDateNormalized = currentMessageDate.setHours(
      0,
      0,
      0,
      0,
    );

    const previousMessageDate = new Date(
      this.messages[messageIndex - 1].sentAt,
    );
    const previousMessageDateNormalized = previousMessageDate.setHours(
      0,
      0,
      0,
      0,
    );

    return currentMessageDateNormalized != previousMessageDateNormalized;
  }
  // if last message or message is 5 mins or more than next message (messageIndex + 1)
  // OR next message is another user
  // THEN showdate
  showMessageDate(messageIndex: number): boolean {
    const lastMessageIndex = this.messages.length - 1;
    if (messageIndex == lastMessageIndex) return true;

    const FIVE_MINUTES = 5 * 60 * 1000;

    const currentMessageDate = new Date(this.messages[messageIndex].sentAt);
    const nextMessageDate = new Date(this.messages[messageIndex + 1].sentAt);

    const currentMessageSender = this.messages[messageIndex].sender.id;
    const nextMessageSender = this.messages[messageIndex + 1].sender.id;

    return (
      nextMessageDate.getTime() - currentMessageDate.getTime() >=
      FIVE_MINUTES || currentMessageSender != nextMessageSender
    );
  }

  // only show sender name and badge if new user messages thread
  showMessageSender(messageIndex: number): boolean {
    // if first message show sender
    if (messageIndex == 0) return true;

    const currentMessageSender = this.messages[messageIndex].sender.id;
    const previousMessageSender = this.messages[messageIndex - 1].sender.id;

    return currentMessageSender != previousMessageSender;
  }

  getRoleLabel(sender: MessagingUser, i: number) {
    const isPatient = sender.userType == MessagingSenderUserType.patient;
    const label = isPatient ? 'Patient' : 'Provider';
    return !this.isUser(sender) && this.showMessageSender(i) ? label : '';
  }
}
