<div class="input-container">
  <!-- Hidden file input -->
  <input
    type="file"
    #fileInput
    style="display: none"
    (change)="onFileSelected($event)"
    (click)="lockAppRefresh()"
    multiple
  />

  <mat-divider />
  <div class="typing-container">
    <bri-buttons
      id="image-upload-button"
      [disabled]="isReadOnly"
      [iconName]="'di di-media86'"
      (buttonClicked)="fileInput.click()"
      [className]="'photo-upload-button'"
    ></bri-buttons>
    <div class="input-box-area">
      <div
        class="images-container"
        *ngFor="let image of userSubmittedImages; let i = index"
      >
        <bri-chips
          [labelText]="image.fileName"
          [removable]="true"
          (removeClicked)="removeImage(i)"
          icon="di-media80"
          iconColor="fileImageColor"
        />
      </div>
      <div
        [ngClass]="
          userSubmittedImages.length > 0
            ? 'image-divider'
            : 'image-divider-no-images'
        "
      >
        <mat-divider *ngIf="userSubmittedImages.length > 0" />
      </div>
      <bri-textarea-fields
        [ngStyle]="{ width: '100%' }"
        id="message-input"
        [label]="'Label'"
        [showMaxLengthErrorText]="false"
        textAreaPlaceholder="Type Message"
        helperText=""
        [maxLengthErrorText]="maxLengthValidationError"
        [textareaFormControl]="formControl"
        [hideOutlineLabel]="true"
        [hideFloatTextAreaLabel]="true"
        [expandable]="true"
        [maxLength]="2054"
        [maxRow]="maxRow"
      ></bri-textarea-fields>
    </div>
    <bri-buttons
      [disabled]="isReadOnly"
      id="send-button"
      [iconName]="'di di-paperplane'"
      [btnText]="deviceType === 'desktop' ? 'Send' : ''"
      (buttonClicked)="onSendButtonClick()"
      [className]="'send-button'"
    ></bri-buttons>
  </div>
</div>
<bri-alert (primaryButtonClick)="closeModal()" />
