import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { MockHttpMessagingService } from 'src/app/messaging/mocks/services';
import { NewMessageFooterComponent } from './new-message-footer.component';
import { CameraService } from 'src/app/services/camera.service';
import { MatDividerModule } from '@angular/material/divider';
import { BriButtonsModule } from '@davita/bridge-library/buttons';
import { BriAlertModule } from '@davita/bridge-library/alert';
import { BriTextareaFieldsModule } from '@davita/bridge-library/fields/textarea';
import {
  BrowserAnimationsModule,
  NoopAnimationsModule,
} from '@angular/platform-browser/animations';
import { BriSnackBarModule } from '@davita/bridge-library/snack-bar';
import { MatSnackBarModule } from '@angular/material/snack-bar';

// Mock CameraService
class MockCameraService {
  isNativePlatform(): boolean {
    return false;
  }

  async selectPhoto() {
    return {
      webPath: 'mock-path',
      format: 'jpeg',
      saved: false,
      file: new File(['mock'], 'mock.jpg', { type: 'image/jpeg' })
    };
  }

  async checkPermissions(): Promise<boolean> {
    return true;
  }

  async requestPermissions(): Promise<boolean> {
    return true;
  }
}

describe('NewMessageFooterComponent', () => {
  let component: NewMessageFooterComponent;
  let fixture: ComponentFixture<NewMessageFooterComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        BriButtonsModule,
        MatDividerModule,
        BriAlertModule,
        BriTextareaFieldsModule,
        BrowserAnimationsModule,
        NoopAnimationsModule,
        BriSnackBarModule,
        MatSnackBarModule,
      ],
      declarations: [NewMessageFooterComponent],
      providers: [
        {
          provide: HttpMessagingService,
          useValue: new MockHttpMessagingService(),
        },
        {
          provide: CameraService,
          useValue: new MockCameraService(),
        },
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(NewMessageFooterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('contains input box for submitting messages', () => {
    const inputElement =
      fixture.debugElement.nativeElement.querySelector('#message-input');
    expect(inputElement).toBeTruthy();
  });

  it('contains send button', () => {
    const sendButton =
      fixture.debugElement.nativeElement.querySelector('#send-button');
    expect(sendButton).toBeTruthy();
  });

  it('contains image upload button', () => {
    const sendButton = fixture.debugElement.nativeElement.querySelector(
      '#image-upload-button',
    );
    expect(sendButton).toBeTruthy();
  });
});
