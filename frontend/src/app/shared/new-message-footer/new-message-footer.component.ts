import {
  Component,
  EventEmitter,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
  forwardRef,
} from '@angular/core';
import {
  ControlValueAccessor,
  FormControl,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { BriAlertComponent } from '@davita/bridge-library/alert';
import { ColorHexService } from '@davita/bridge-library/color-hex.service';
import { DarkModeService } from '@davita/bridge-library/dark-mode.service';
import { DialogData } from '@davita/bridge-library/shared';
import { CapcitorAppRefreshService } from '@davita/bridge-library/capacitor-app-refresh';
import { debounceTime, mergeMap } from 'rxjs';
import { ImageMessageData } from 'src/app/models';
import { DeviceStateService } from 'src/app/services/device-state.service';
import { SnackbarService } from 'src/app/services/snackbar.service';

const IMAGE_UPLOAD_LIMIT = 3;

@Component({
  selector: 'app-new-message-footer',
  templateUrl: './new-message-footer.component.html',
  styleUrls: ['./new-message-footer.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => NewMessageFooterComponent),
      multi: true,
    },
  ],
})
export class NewMessageFooterComponent
  implements ControlValueAccessor, OnInit, OnDestroy
{
  @ViewChild(BriAlertComponent, { static: false })
  private component!: BriAlertComponent;
  @Input() formControl: FormControl = new FormControl('');
  @Input() isReadOnly: boolean = false;
  @Output() sendButtonClick = new EventEmitter<ImageMessageData[] | void>();
  fileImageColor = '';
  userSubmittedImages: ImageMessageData[] = [] as ImageMessageData[]; // stores images uploaded by user
  maxRow: number = 24;
  deviceType: string = this.deviceState.determineDevice();
  allowedFormats: string[] = [
    'image/gif',
    'image/png',
    'image/eps',
    'image/tiff',
    'image/bmp',
    'image/jpeg',
    'image/jpg',
  ];
  maxLengthValidationError: string =
    'Message too long, please limit to 2054 characters.';

  public screenLocked = false;

  constructor(
    private deviceState: DeviceStateService,
    private snackbar: SnackbarService,
    private colorHexService: ColorHexService,
    private darkModeService: DarkModeService,
    private capacitorAppRefreshService: CapcitorAppRefreshService,
  ) {}

  ngOnDestroy(): void {
    this.capacitorAppRefreshService.setlockCapacitorAppRefresh(false);
  }

  lockAppRefresh = () => {
    if (!this.screenLocked) {
      this.screenLocked = true;
      this.capacitorAppRefreshService.setlockCapacitorAppRefresh(true);
    }
  };

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;

    // Check if the input has files
    if (
      input.files &&
      input.files.length &&
      input.files.length <= IMAGE_UPLOAD_LIMIT
    ) {
      for (let i = 0; i < input.files.length; i++) {
        const file = input.files[i];
        if (this.allowedFormats.includes(file.type.toLowerCase())) {
          if (this.userSubmittedImages.length < IMAGE_UPLOAD_LIMIT) {
            this.userSubmittedImages.push({
              imageFile: file,
              fileName: file.name,
            });
          } else {
            this.snackbar.openErrorToast(
              `You can only submit up to ${IMAGE_UPLOAD_LIMIT} images per message`,
            );
          }
        } else {
          this.snackbar.openErrorToast(
            `File of type ${file.type} is not allowed.`,
          );
        }
      }
    } else if (input.files && input.files?.length > IMAGE_UPLOAD_LIMIT) {
      this.snackbar.openErrorToast(
        'You can only submit up to 3 images per message',
      );
    }

    // reset input , so user can re-add removed chip
    input.value = '';
  }

  removeImage(index: number): void {
    if (index < this.userSubmittedImages.length)
      this.userSubmittedImages.splice(index, 1);
    if (
      this.formControl.value.length === 0 &&
      this.userSubmittedImages.length === 0 &&
      this.screenLocked
    ) {
      this.screenLocked = false;
      this.capacitorAppRefreshService.setlockCapacitorAppRefresh(false);
    }
  }

  onSendButtonClick(): void {
    if (this.screenLocked) {
      this.capacitorAppRefreshService.setlockCapacitorAppRefresh(false);
      this.screenLocked = false;
    }
    if (this.userSubmittedImages.length > 0) {
      this.sendButtonClick.emit(this.userSubmittedImages);
      this.userSubmittedImages = [];
    } else if (this.formControl.value) this.sendButtonClick.emit();
    else {
      this.openErrorModal();
    }
  }

  openErrorModal() {
    const data: DialogData = {
      header: 'We need more information.',
      description:
        'It looks like you forgot to include a subject line or message. Please make sure to add both to send your message.',
      primaryButtonLabel: 'Okay',
      hideSecondaryButton: true,
    };
    this.component.open(data);
  }

  closeModal() {
    this.component.close();
  }

  // THIS IS BOILERPLATE CODE REQUIRED TO AVOID NG01203 ERROR
  // https://angular.io/errors/NG01203
  // TODO - we may be able to fix this in the bridge library
  // eslint-disable-next-line
  writeValue(_obj: any): void {
    // Write value from the model to the view
  }

  // eslint-disable-next-line
  registerOnChange(_fn: any): void {
    // Register a handler that should be called when something in the view changes
  }

  // eslint-disable-next-line
  registerOnTouched(_fn: any): void {
    // Register a handler specifically for when the component is touched
  }

  // eslint-disable-next-line
  setDisabledState?(_isDisabled: boolean): void {
    // Handle the disabled state
  }

  ngOnInit() {
    this.formControl.valueChanges.pipe(debounceTime(100)).subscribe((value) => {
      if (!this.screenLocked && value.length > 0) {
        this.screenLocked = true;
        this.capacitorAppRefreshService.setlockCapacitorAppRefresh(true);
      }
      if (
        value.length === 0 &&
        this.userSubmittedImages.length === 0 &&
        this.screenLocked
      ) {
        this.screenLocked = false;
        this.capacitorAppRefreshService.setlockCapacitorAppRefresh(false);
      }
    });
    this.maxRow = this.calculateMaxRows();

    this.darkModeService.darkMode$
      .pipe(
        mergeMap((darkMode) => {
          const mode = darkMode ? 'dark' : 'light';
          return this.colorHexService.getColorHex(`${mode}-color-text-50`);
        }),
      )
      .subscribe((hexColor) => {
        this.fileImageColor = hexColor;
      });
  }

  @HostListener('window:resize')
  onResize(): void {
    this.maxRow = this.calculateMaxRows();
    this.deviceType = this.deviceState.determineDevice();
  }

  calculateMaxRows(): number {
    const screenHeight = window.innerHeight;
    if (screenHeight >= 1050) {
      return 24;
    } else if (screenHeight >= 950) {
      return 20;
    } else if (screenHeight >= 850) {
      return 16;
    } else if (screenHeight >= 700) {
      return 12;
    } else if (screenHeight >= 600) {
      return 8;
    } else {
      return 4;
    }
  }

  determineDevice(): string {
    if (window.innerWidth < 600) {
      return 'mobile';
    } else if (600 <= window.innerWidth && window.innerWidth < 960) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }
}
