<div [ngClass]="isWidget ? 'message-widget' : 'right-message-panel'">
  <ng-container *ngIf="newMessageStatus !== Status.loading; else loadingState">
      <ng-container
        *ngIf="
          !isTeammate || selectedPatient !== undefined;
          else missingPatient
        "
      >
        <app-message-header
          *ngIf="!isTeammate"
          [subjectFormControl]="subjectFormControl"
          [hasUserInputedText]="hasUserInputedText"
          (newMessageClosed)="redirectUserToLandingPage()"
        />
        <app-message-header
          *ngIf="isTeammate"
          [subject]="getPatientNameAndMpi()"
          [subjectFormControl]="subjectFormControl"
          [hasUserInputedText]="hasUserInputedText"
          (newMessageClosed)="redirectUserToLandingPage()"
        />
        <div class="message-area">
          <p>Today {{ currentDate | date: "h:mm a" }}</p>
        </div>

        <div class="footer">
          <div class="disclaimer">
            <mat-divider />
            <div class="disclaimer-text">
              <p class="mat-caption">
                Messages are viewed by all members of your care team.
              </p>
            </div>
          </div>

          <app-new-message-footer
            [formControl]="newMessageFormControl"
            (sendButtonClick)="createNewThreadWithMessage($event)"
          >
          </app-new-message-footer>
        </div>
      </ng-container>
      <ng-template #missingPatient>
        <div class="patient-select">
          <app-patient-select (patientSelected)="setPatientMpi($event)">
          </app-patient-select>
        </div>
      </ng-template>
  </ng-container>

  <ng-template #loadingState>
    <div class="loading">
      <bri-progress-bar message="Sending Message"></bri-progress-bar>
    </div>
  </ng-template>
</div>

<!-- Missing Subject or Message Modal -->
<bri-alert #missingFieldError (primaryButtonClick)="closeModal()" />

<!-- Message Sent Modal -->
<bri-alert
  #messageSent
  (primaryButtonClick)="redirectUserToMessagingMFE()"
  (secondaryButtonClick)="closeMessageSentModal()"
/>
