@import "@davita/bridge-library/scss/colors";

.message-widget {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 0.25px solid $light-color-blue-dark;
  @media (prefers-color-scheme: dark) {
    border: 0.25px solid $dark-color-blue-dark;
  }
  width: 505px;
  height: 568px;
  gap: 0px;
  opacity: 1;
  background-color: $light-color-ui-background !important;
  @media (prefers-color-scheme: dark) {
    background-color: $dark-color-ui-background !important;
  }
  border-radius: 8px;
  border: 0.25px 0px 0px 0px;
  box-shadow: 0px 16px 16px 0px #00000040;
  z-index: 1000;
  position: relative;
}

.message-title-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-right: 105px;
  .center {
    flex-grow: 1;
    text-align: center;
  }
  .cancel-button {
    margin-left: 8px;
  }
}

.subject-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 16px 16px 0;
  width: 100%;
  p {
    padding: 8px 16px;
  }
}
:host ::ng-deep {
  app-optimized-fields {
    .checkbox-form {
      display: flex;
      margin: 16px 0 24px;
      flex-direction: row;
      gap: 24px;
    }
    .mat-mdc-card-outlined {
      background-color: $light-color-ui-background !important;
      @media (prefers-color-scheme: dark) {
        background-color: $dark-color-ui-background !important;
      }
    }
    .beta-note {
      margin: 0 0 16px 0 !important;
    }
    .mat-mdc-tab-body-wrapper {
      padding: 20px 0;
    }
    .mat-mdc-card {
      padding: 20px;
    }
  }
}
.message-area {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 482px;
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100vh - 150px);
  padding-bottom: 60px;
  position: relative;

  p {
    padding-top: 16px;
    color: $light-color-text-25;
  }

  .messages-display {
    width: 100%;
    padding: 16px;
    overflow: scroll;
  }
}

.right-message-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.loading {
  display: flex;
  justify-content: center; /* Aligns horizontally */
  align-items: center; /* Aligns vertically */
  margin-top: 35vh;
}

.disclaimer {
  width: 100%;
  padding: 8px 16px;
  mat-divider {
    border: 1px solid $light-color-ui-separator;
    @media (prefers-color-scheme: dark) {
      border: 1px solid $dark-color-ui-separator;
    }
  }
  .disclaimer-text {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 8px;
    color: $light-color-text-25;
    text-align: center;
    @media (prefers-color-scheme: dark) {
      color: $dark-color-text-25;
    }
  }
}

.patient-select {
  height: 100%;
}

@media screen and (max-width: 482px) {
  .message-area {
    min-width: 0;
  }
}

@media screen and (min-width: 768px) {
  .message-widget {
    position: fixed;
    right: 16px;
    bottom: 16px;
  }
}

@media screen and (max-width: 768px) {
  .message-widget {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    border: none;
    box-shadow: none;
    padding-top: 50px;

    .message-area {
      min-width: 0;
    }
  }
}

@media screen and (max-width: 960px) {
  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: $light-color-ui-background;
    @media (prefers-color-scheme: dark) {
      background-color: $dark-color-ui-background;
    }
  }
}
