import { ComponentFixture, TestBed } from '@angular/core/testing';

import { NewMessageComponent } from './new-message.component';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { BriSnackBarModule } from '@davita/bridge-library/snack-bar';
import { BriAlertModule } from '@davita/bridge-library/alert';
import { MessageHeaderComponent } from '../message-header/message-header.component';
import { NewMessageFooterComponent } from '../new-message-footer/new-message-footer.component';
import { MatDividerModule } from '@angular/material/divider';
import { BriButtonsModule } from '@davita/bridge-library/buttons';
import { BriTextFieldsModule } from '@davita/bridge-library/fields/text';
import {
  BrowserAnimationsModule,
  NoopAnimationsModule,
} from '@angular/platform-browser/animations';
import { BriTextareaFieldsModule } from '@davita/bridge-library/fields/textarea';
import { MockHttpMessagingService } from '../mocks/services';
import { BriSubHeaderModule } from '@davita/bridge-library/sub-header';
import { By } from '@angular/platform-browser';

describe('NewMessageComponent', () => {
  let component: NewMessageComponent;
  let fixture: ComponentFixture<NewMessageComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        BriButtonsModule,
        BriSnackBarModule,
        BriAlertModule,
        MatDividerModule,
        BriTextFieldsModule,
        BrowserAnimationsModule,
        NoopAnimationsModule,
        BriTextareaFieldsModule,
        BriSubHeaderModule,
      ],
      declarations: [
        NewMessageComponent,
        MessageHeaderComponent,
        NewMessageFooterComponent,
      ],
      providers: [
        {
          provide: HttpMessagingService,
          useValue: new MockHttpMessagingService(),
        },
        HttpClient,
        HttpHandler,
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(NewMessageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should contain date', () => {
    const compiled = fixture.debugElement.nativeElement;
    const inputTitle = compiled.querySelector('.right-message-panel');
    expect(inputTitle.textContent).toContain('Today');
  });

  it('should contain disclaimer text', () => {
    const compiled = fixture.debugElement.nativeElement;
    const inputTitle = compiled.querySelector('.disclaimer');
    expect(inputTitle.textContent).toContain(
      'Messages are viewed by all members of your care team.',
    );
  });

  it('contains input box for subject', () => {
    const inputElement =
      fixture.debugElement.nativeElement.querySelector('#subject');
    expect(inputElement).toBeTruthy();
  });

  it('contains input box for submitting messages', () => {
    const inputElement =
      fixture.debugElement.nativeElement.querySelector('#message-input');
    expect(inputElement).toBeTruthy();
  });

  it('contains 3 buttons', () => {
    const buttons = fixture.debugElement.queryAll(By.css('button'));
    expect(buttons.length).toEqual(3);
  });

  it('contains cancel button', () => {
    const buttons = fixture.debugElement.queryAll(By.css('button'));
    const cancelButton = Array.from(buttons).find(
      (button) => button.nativeElement.textContent?.trim() === 'Cancel',
    );
    expect(cancelButton).toBeTruthy();
  });

  it('contains send button', () => {
    const sendButton =
      fixture.debugElement.nativeElement.querySelector('#send-button');
    expect(sendButton).toBeTruthy();
  });

  it('contains image upload button', () => {
    const sendButton = fixture.debugElement.nativeElement.querySelector(
      '#image-upload-button',
    );
    expect(sendButton).toBeTruthy();
  });

  it('cancel modal appears when user clicks cancel button', () => {
    const buttons = fixture.debugElement.queryAll(By.css('button'));
    const cancelButton = Array.from(buttons).find(
      (button) => button.nativeElement.textContent?.trim() === 'Cancel',
    );

    cancelButton?.triggerEventHandler('click', null);

    fixture.detectChanges();
    const modalElement = document.querySelector('.cdk-overlay-container');
    expect(modalElement).toBeTruthy();
    expect(modalElement?.textContent).toContain('Cancel your new message?');
  });
});
