import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
  EMPTY,
  Subscription,
  catchError,
  combineLatest,
  skipWhile,
  startWith,
  tap,
} from 'rxjs';
import { SnackbarService } from 'src/app/services/snackbar.service';
import {
  ImageMessageData,
  Message,
  MessagingPatient,
  Status,
} from 'src/app/models';
import { BriAlertComponent, DialogData } from '@davita/bridge-library/alert';
import { MessagingProxyService } from 'src/app/services/messaging-proxy.service';
import { UserTypeService } from 'src/app/services/user-type.service';
import { UserType } from 'src/app/models/roles';

@Component({
  selector: 'app-new-message',
  templateUrl: './new-message.component.html',
  styleUrls: ['./new-message.component.scss'],
})
export class NewMessageComponent implements OnInit, OnDestroy {
  constructor(
    private router: Router,
    private proxyService: MessagingProxyService,
    private snackbarService: SnackbarService,
    private route: ActivatedRoute,
    private userTypeService: UserTypeService,
  ) { }

  @ViewChild('missingFieldError', { static: false })
  private component!: BriAlertComponent;
  @ViewChild('messageSent', { static: false })
  private messageSentComponent!: BriAlertComponent;
  @Output() closeMessagingPanel = new EventEmitter<void>();
  @Input() isWidget = false;
  currentDate: Date = new Date();
  newMessageStatus?: Status;
  subjectFormControl: FormControl = new FormControl<string>(
    '',
    Validators.maxLength(140),
  );
  newMessageFormControl: FormControl = new FormControl<string>('', [
    Validators.maxLength(2054),
  ]);
  Status = Status;
  formChangeSubscription = new Subscription();
  hasUserInputedText = false;
  messageThreadId = '';
  public isTeammate = false;
  public selectedPatient?: MessagingPatient;

  ngOnInit(): void {
    this.newMessageStatus = Status.loading;
    this.userTypeService.userType$
      .pipe(
        skipWhile((user) => user == undefined),
        tap((user) => {
          this.isTeammate = user == UserType.TEAMMATE;
          this.newMessageStatus = Status.success;
        }),
      )
      .subscribe();
    this.formChangeSubscription = combineLatest([
      this.newMessageFormControl.valueChanges.pipe(
        startWith(this.newMessageFormControl.value),
      ),
      this.subjectFormControl.valueChanges.pipe(
        startWith(this.subjectFormControl.value),
      ),
    ]).subscribe(([message, subject]: [string, string]) => {
      this.hasUserInputedText = !!message || !!subject;
    });
  }

  ngOnDestroy(): void {
    this.formChangeSubscription.unsubscribe();
  }

  createNewThreadWithMessage(imagesData: ImageMessageData[] | void) {
    this.newMessageStatus = Status.loading;

    // check message text length
    if (
      this.newMessageFormControl.errors &&
      this.newMessageFormControl.errors['maxlength']
    ) {
      this.snackbarService.openErrorToast(
        'Message is too long, please limit to 2054 characters.',
      );
      this.newMessageStatus = Status.error;
      return;
    }

    if (
      this.subjectFormControl.valid &&
      this.subjectFormControl.value &&
      (imagesData || this.newMessageFormControl.value)
    ) {
      // if there are images , prepare them
      let imageFiles: File[] = [] as File[];
      let filename = '';
      if (imagesData) {
        filename = imagesData[0].fileName;
        imageFiles = imagesData.map((image) => image.imageFile);
      }

      this.proxyService
        .getHttpService()
        .postNewThreadPlusFirstMessage(
          this.subjectFormControl.value,
          this.newMessageFormControl.value || filename,
          imageFiles,
          this.selectedPatient?.masterPatientIdentifier,
        )
        .pipe(
          catchError(() => {
            this.snackbarService.openErrorToast('Message Not Delivered');
            this.newMessageStatus = Status.error;
            return EMPTY;
          }),
        )
        .subscribe((message: Message) => {
          this.messageThreadId = message.threadId;

          if (this.isWidget) {
            this.MessageSentModal();
            this.newMessageStatus = Status.success;
            this.closeMessagingPanel.emit();
          } else {
            this.snackbarService.openSuccessToast('Message Sent');
            this.router.navigate([`../thread/${this.messageThreadId}`], {
              skipLocationChange: true,
              relativeTo: this.route,
            });
          }
        });
    } else {
      this.newMessageStatus = Status.error;
      if (!this.subjectFormControl.value) this.MissingFieldErrorModal();
      else if (!this.subjectFormControl.valid) this.SubjectTooLongErrorModal();
      else this.MissingFieldErrorModal();
    }
  }

  redirectUserToLandingPage() {
    if (this.isWidget) {
      this.closeMessagingPanel.emit();
    } else {
      this.router.navigate(['../landing'], {
        skipLocationChange: true,
        relativeTo: this.route,
      }); //use for IKC
    }
  }

  redirectUserToMessagingMFE() {
    this.router.navigate([`../thread/${this.messageThreadId}`], {
      skipLocationChange: true,
      relativeTo: this.route,
    });
  }

  closeModal() {
    this.component.close();
  }

  closeMessageSentModal() {
    this.messageSentComponent.close();
  }

  setPatientMpi(patient: MessagingPatient) {
    this.selectedPatient = patient;
  }

  getPatientNameAndMpi() {
    return `${this.selectedPatient?.fullName || ''} - MPI: ${this.selectedPatient?.masterPatientIdentifier}`;
  }

  MissingFieldErrorModal() {
    const data: DialogData = {
      header: 'We need more information.',
      description:
        'It looks like you forgot to include a subject line or message. Please make sure to add both to send your message.',
      primaryButtonLabel: 'Okay',
      hideSecondaryButton: true,
    };
    this.component.open(data);
  }

  SubjectTooLongErrorModal() {
    const data: DialogData = {
      header: 'Subject is too long.',
      description:
        'Subject cannot be more than 140 characters long.  Please keep your subject short.',
      primaryButtonLabel: 'Okay',
      hideSecondaryButton: true,
    };
    this.component.open(data);
  }

  MessageSentModal() {
    const data: DialogData = {
      header: 'Message Sent!',
      description:
        "Your message was sent to your care team. If you'd like to view the message thread, please go to your Messages.",
      primaryButtonLabel: 'VIEW MESSAGES',
      secondaryButtonLabel: 'CLOSE',
    };
    this.messageSentComponent.open(data);
  }
}
