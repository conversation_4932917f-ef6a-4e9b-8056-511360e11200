<div class="patient-select-container">
  <h2 class="patient-select-title">Select Patient</h2>
  <ng-container *ngIf="patientOptions !== undefined; else loadingState">
    <ng-container *ngIf="patientOptions.length > 0; else emptyState">
      <div class="patient-select-list">
        <bri-master-list-item-v2
          [type]="'radio'"
          [multipleInputList]="patientOptions"
          [radioFormControlName]="formControlName"
          [getFormBuilderGroup]="patientSelectForm"
        >
        </bri-master-list-item-v2>
      </div>
      <div class="patient-select-footer">
        <bri-buttons
          [disabled]="patientSelectForm.invalid"
          briFlat
          [btnText]="'Select Patient'"
          (buttonClicked)="trySelectUser()"
        ></bri-buttons>
      </div>
    </ng-container>
    <ng-template #emptyState>
      <bri-empty-state
        [isCircleBorder]="false"
        [isEmptyStateSVG]="false"
        [iconClass]="'di di-alert'"
        [headerText]="'Error'"
        [messageText]="'Unable to get patients, try again later'"
      ></bri-empty-state>
    </ng-template>
  </ng-container>
  <ng-template #loadingState>
    <div class="loading">
      <bri-progress-bar message="Getting Patients..."></bri-progress-bar>
    </div>
  </ng-template>
</div>
