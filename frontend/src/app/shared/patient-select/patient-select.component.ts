import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { catchError, map, of } from 'rxjs';
import { MessagingPatient } from 'src/app/models';
import { SnackbarService } from 'src/app/services/snackbar.service';
import { UserService } from 'src/app/services/user.service';

@Component({
  selector: 'app-patient-select',
  templateUrl: './patient-select.component.html',
  styleUrls: ['./patient-select.component.scss'],
})
export class PatientSelectComponent implements OnInit {
  @Output() patientSelected = new EventEmitter<MessagingPatient>();

  public patientOptions?: any[];
  public patientSelectForm: FormGroup;
  public readonly formControlName = 'patientRadio';

  constructor(
    private formBuilder: FormBuilder,
    private userService: UserService,
    private snackbarService: SnackbarService,
  ) {
    this.patientSelectForm = this.formBuilder.group({
      patientRadio: ['', [Validators.required]],
    });
  }

  ngOnInit(): void {
    this.userService
      .getAllPatientsForFacility()
      .pipe(
        map((patients) => {
          if (!patients.length) {
            throw new Error('Patient list empty');
          }
          patients = patients.sort((a, b) => {
            if (a.lastName < b.lastName) {
              return -1;
            } else if (a.lastName > b.lastName) {
              return 1;
            } else {
              return 1;
            }
          });
          this.patientOptions = patients.map((p) => {
            return {
              name: `${p.firstName}${p.lastName}`,
              value: p,
              textComboType: 'line2',
              textList: [
                {
                  name: 'line1',
                  text: p.fullName,
                  className: 'mat-subtitle-1',
                },
                {
                  name: 'line2',
                  text: `MPI: ${p.masterPatientIdentifier}`,
                  class: 'mat-body',
                },
              ],
            };
          });
        }),
        catchError((err) => {
          console.error(err);
          this.patientOptions = [];
          return of([]);
        }),
      )
      .subscribe();
  }

  public trySelectUser() {
    this.patientSelectForm.disable();
    const patient: MessagingPatient =
      this.patientSelectForm.value[this.formControlName];
    this.userService
      .getPatientStatus(patient.masterPatientIdentifier)
      .pipe(
        map((active) => {
          if (active) {
            this.patientSelected.emit(patient);
            return active;
          }
          throw new Error('Patient inactive');
        }),
        catchError((err) => {
          console.error(err);
          this.snackbarService.openErrorToast(
            "Error: unable to validate patient's active status",
          );
          this.patientSelectForm.enable();
          return of(null);
        }),
      )
      .subscribe();
  }
}
