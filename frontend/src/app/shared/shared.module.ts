import { NgModule } from '@angular/core';
import { BriIconModule } from '@davita/bridge-library/icon';
import { BriButtonsModule } from '@davita/bridge-library/buttons';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import {
  BriSimpleDialogComponent,
  BriSimpleDialogModule,
} from '@davita/bridge-library/simple-dialog';
import { ImageModalComponent } from './image-modal/image-modal.component';
import { ImageComponent } from './image/image.component';
import { LandingComponent } from './landing/landing.component';
import { ThreadMessagesComponent } from './thread-messages/thread-messages.component';
import { MessageStreamComponent } from './message-stream/message-stream.component';
import { NewMessageComponent } from './new-message/new-message.component';
import { NewMessageFooterComponent } from './new-message-footer/new-message-footer.component';
import { MessageHeaderComponent } from './message-header/message-header.component';
import { MessageDatePipe } from './message-date.pipe';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BriAlertModule } from '@davita/bridge-library/alert';
import { BriBadgeModule } from '@davita/bridge-library/badge';
import { BriMasterListItemModule } from '@davita/bridge-library/master-list-item';
import { BriMasterListItemV2Module } from '@davita/bridge-library/master-list-item-v2';
import { MatDividerModule } from '@angular/material/divider';
import { BriFieldsModule } from '@davita/bridge-library/fields';
import { BriChipsModule } from '@davita/bridge-library/chips';
import { HttpClientModule } from '@angular/common/http';
import { BriTextFieldsModule } from '@davita/bridge-library/fields/text';
import { MatCardModule } from '@angular/material/card';
import { BriMessageModule } from '@davita/bridge-library/message';
import { BriChatMessageModule } from '@davita/bridge-library/chat-message';
import { MatListModule } from '@angular/material/list';
import { BriProgressBarModule } from '@davita/bridge-library/progress-bar';
import { BriSnackBarModule } from '@davita/bridge-library/snack-bar';
import { BriTextareaFieldsModule } from '@davita/bridge-library/fields/textarea';
import { MatDialogModule } from '@angular/material/dialog';
import { BriSubHeaderV2Module } from '@davita/bridge-library/sub-header-v2';
import { BriEmptyStateModule } from '@davita/bridge-library/empty-state';
import { PatientSelectComponent } from './patient-select/patient-select.component';

@NgModule({
  imports: [
    CommonModule,
    BriButtonsModule,
    BriSimpleDialogModule,
    MatIconModule,
    BriIconModule,
    ReactiveFormsModule,
    FormsModule,
    BriAlertModule,
    BriButtonsModule,
    BriBadgeModule,
    BriMasterListItemModule,
    MatDividerModule,
    BriFieldsModule,
    BriChipsModule,
    HttpClientModule,
    BriTextFieldsModule,
    MatCardModule,
    BriMessageModule,
    BriChatMessageModule,
    MatListModule,
    BriProgressBarModule,
    BriIconModule,
    BriSimpleDialogModule,
    BriSnackBarModule,
    BriTextareaFieldsModule,
    MatDialogModule,
    MatIconModule,
    BriAlertModule,
    BriSubHeaderV2Module,
    BriEmptyStateModule,
    BriMasterListItemV2Module,
  ],
  exports: [
    BriSimpleDialogComponent,
    ImageModalComponent,
    ImageComponent,
    LandingComponent,
    ThreadMessagesComponent,
    MessageStreamComponent,
    NewMessageComponent,
    NewMessageFooterComponent,
    MessageHeaderComponent,
    MessageDatePipe,
    PatientSelectComponent,
    ReactiveFormsModule,
    FormsModule,
    BriAlertModule,
    BriButtonsModule,
    BriBadgeModule,
    BriMasterListItemModule,
    MatDividerModule,
    BriFieldsModule,
    BriChipsModule,
    HttpClientModule,
    BriTextFieldsModule,
    MatCardModule,
    BriMessageModule,
    BriChatMessageModule,
    MatListModule,
    BriProgressBarModule,
    BriIconModule,
    BriSimpleDialogModule,
    BriSnackBarModule,
    BriTextareaFieldsModule,
    MatDialogModule,
    MatIconModule,
    BriAlertModule,
    BriSubHeaderV2Module,
    BriEmptyStateModule,
    BriMasterListItemV2Module,
  ],
  declarations: [
    ImageModalComponent,
    ImageComponent,
    LandingComponent,
    ThreadMessagesComponent,
    MessageStreamComponent,
    NewMessageComponent,
    NewMessageFooterComponent,
    MessageHeaderComponent,
    MessageDatePipe,
    PatientSelectComponent,
  ],
})
export class SharedModule {}
