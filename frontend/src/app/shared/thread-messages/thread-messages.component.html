<div class="right-message-panel">
  <bri-sub-header-v2
    *ngIf="deviceType !== 'desktop'"
    [leftContent]="{ showBackButton: true, backText: 'Close' }"
    (backBtnClicked)="closeMessage()"
    [hideBackIcon]="true"
    [sectionHeader]="getTitle()"
  />
  <app-message-header
    [readOnly]="true"
    [subject]="getTitle()"
    *ngIf="deviceType === 'desktop'"
  />
  <div *ngIf="isTeammate">
    <app-message-header [readOnly]="true" [subject]="getSubject()" />
    <mat-divider></mat-divider>
  </div>

  <!-- Pagination Loading Spinner -->
  <div *ngIf="showSpinner" class="loading-spinner">
    <div class="spinner"></div>
  </div>

  <!-- Loading Bar  -->
  <ng-container *ngIf="newMessageStatus === Status.loading">
    <bri-progress-bar width="100%" />
  </ng-container>

  <div class="message-area" #scrollContainer>
    <mat-list [ngClass]="'messages-display'">
      <!-- MessageStream is a view component that renders messages of thread "id" -->
      <app-message-stream
        [id]="id"
        [scrollContainer]="scrollContainerRef"
        (newPageLoaded)="onNewPageLoaded($event)"
        (newPageLoading)="onNewPageLoading($event)"
        [triggerInfiniteScroll$]="triggerInfiniteScroll$"
        (retryButtonClicked)="openRetryModal()"
      >
      </app-message-stream>
    </mat-list>
  </div>

  <div class="footer">
    <app-new-message-footer
      [formControl]="newMessageFormControl"
      [isReadOnly]="readOnlyThread"
      (sendButtonClick)="sendMessage($event)"
    />
  </div>
</div>

<!-- Retry Logic Modal -->
<bri-alert
  (primaryButtonClick)="tryAgainButton()"
  (secondaryButtonClick)="closeModal()"
/>
