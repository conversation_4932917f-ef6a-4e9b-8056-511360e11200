@import "@davita/bridge-library/scss/colors";

.right-message-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.message-area {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: auto;
  min-width: 482px;
  p {
    padding-top: 16px;
    color: $light-color-text-25;
  }
  .messages-display {
    height: fit-content;
    width: 100%;
    padding: 16px;
  }
}
bri-sub-header-v2 ::ng-deep div.header-container {
  border-bottom: 1px solid $light-color-ui-disabled;
  @media (prefers-color-scheme: dark) {
    border-bottom: 1px solid $dark-color-ui-disabled;
  }
}
.input-container {
  width: 100%;
  .disclaimer {
    width: 100%;
    padding: 8px 16px;
    mat-divider {
      border: 1px solid $light-color-ui-separator;
      @media (prefers-color-scheme: dark) {
        border: 1px solid $dark-color-ui-separator;
      }
    }
    .disclaimer-text {
      display: flex;
      justify-content: center;
      align-items: center;
      padding-top: 8px;
      color: $light-color-text-25;
      text-align: center;
      @media (prefers-color-scheme: dark) {
        color: $dark-color-text-25;
      }
    }
  }
}
.typing-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  gap: 10px;
  .photo-upload-button i .icon {
    font-size: 24px;
  }
}

.error {
  display: flex;
  flex-direction: row;
  justify-content: flex-end; /* Aligns horizontally */
  align-items: center; /* Aligns vertically */
}

.user_name {
  color: $light-color-text-100;
  text-align: right;
  padding-top: 8px;
  font-size: 16px;
}

.loading {
  width: 100%;
}

.loading-spinner {
  margin-top: 7px;
  display: flex;
  justify-content: center;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1); /* Light grey border */
  border-left-color: #09f; /* Blue color for the spinner part */
  border-radius: 50%; /* Make it circular */
  width: 30px; /* Size of the spinner */
  height: 30px; /* Size of the spinner */
  animation: spin 1s linear infinite; /* Animation applied to the spinner */
}

@keyframes spin {
  0% {
    transform: rotate(0deg); /* Start position */
  }
  100% {
    transform: rotate(360deg); /* End position */
  }
}

@media screen and (max-width: 482px) {
  .message-area {
    min-width: 0;
  }
}

@media screen and (max-width: 960px) {
  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: $light-color-ui-background;
    @media (prefers-color-scheme: dark) {
      background-color: $dark-color-ui-background;
    }
  }
}

@media screen and (max-width: 959px) {
  .right-message-panel {
    bri-sub-header-v2 {
      ::ng-deep .header-container {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        z-index: 999;
        margin-top: env(safe-area-inset-top, 0);
      }
    }
  }
}
//
