import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { MockHttpMessagingService } from '../mocks/services';
import { ThreadMessagesComponent } from './thread-messages.component';
import { ActivatedRoute } from '@angular/router';
import {
  MockBriPatientAuthorizationService,
  activatedRouteMock,
} from '../mocks/services';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { BriPatientAuthorizationService } from '@davita/bridge-utility-library/authorization';
import { BriSnackBarModule } from '@davita/bridge-library/snack-bar';
import { MessageStreamComponent } from '../message-stream/message-stream.component';
import { NewMessageComponent } from '../new-message/new-message.component';
import { NewMessageFooterComponent } from '../new-message-footer/new-message-footer.component';
import { BriAlertModule } from '@davita/bridge-library/alert';
import { MatDividerModule } from '@angular/material/divider';
import { BriButtonsModule } from '@davita/bridge-library/buttons';
import { MessageHeaderComponent } from '../message-header/message-header.component';
import { BriProgressBarModule } from '@davita/bridge-library/progress-bar';
import { BriTextareaFieldsModule } from '@davita/bridge-library/fields/textarea';
import {
  BrowserAnimationsModule,
  NoopAnimationsModule,
} from '@angular/platform-browser/animations';
import { BriSubHeaderModule } from '@davita/bridge-library/sub-header';
import { MessageDatePipe } from '../message-date.pipe';
import { CommonModule, DatePipe } from '@angular/common';
import { BriChatMessageModule } from '@davita/bridge-library/chat-message';
import { mockMessages, mockThread } from '../mocks/data';

describe('ThreadMessagesComponent', () => {
  let component: ThreadMessagesComponent;
  let fixture: ComponentFixture<ThreadMessagesComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        BriSnackBarModule,
        BriAlertModule,
        MatDividerModule,
        BriButtonsModule,
        BriProgressBarModule,
        BriTextareaFieldsModule,
        BrowserAnimationsModule,
        NoopAnimationsModule,
        BriSubHeaderModule,
        BriChatMessageModule,
      ],
      declarations: [
        ThreadMessagesComponent,
        MessageStreamComponent,
        NewMessageComponent,
        NewMessageFooterComponent,
        MessageHeaderComponent,
        MessageDatePipe,
      ],
      providers: [
        DatePipe,
        {
          provide: BriPatientAuthorizationService,
          useClass: MockBriPatientAuthorizationService,
        },
        { provide: ActivatedRoute, useValue: activatedRouteMock },
        {
          provide: HttpMessagingService,
          useValue: new MockHttpMessagingService(),
        },
        HttpClient,
        HttpHandler,
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(ThreadMessagesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should contain thread title at top', () => {
    const compiled = fixture.debugElement.nativeElement;
    const title = compiled.querySelector('.right-message-panel');
    expect(title.textContent).toContain(mockThread.subject);
  });

  it('should contain 1st users messages', () => {
    const compiled = fixture.debugElement.nativeElement;
    const title = compiled.querySelector('.message-area');
    expect(title.textContent).toContain(mockMessages[0].messageText);
    expect(title.textContent).toContain(mockMessages[0].sender.firstName);
  });

  it('should contain 2nd users messages', () => {
    const compiled = fixture.debugElement.nativeElement;
    const title = compiled.querySelector('.message-area');
    expect(title.textContent).toContain(mockMessages[1].messageText);
    expect(title.textContent).toContain(mockMessages[1].sender.firstName);
  });

  it('should contain 3nd users messages', () => {
    const compiled = fixture.debugElement.nativeElement;
    const title = compiled.querySelector('.message-area');
    expect(title.textContent).toContain(mockMessages[2].messageText);
    expect(title.textContent).toContain(mockMessages[2].sender.firstName);
  });

  it('should contain 4nd users messages', () => {
    const compiled = fixture.debugElement.nativeElement;
    const title = compiled.querySelector('.message-area');
    expect(title.textContent).toContain(mockMessages[3].messageText);
    expect(title.textContent).toContain(mockMessages[3].sender.firstName);
  });

  it('should contain 5nd users messages', () => {
    const compiled = fixture.debugElement.nativeElement;
    const title = compiled.querySelector('.message-area');
    expect(title.textContent).toContain(mockMessages[4].messageText);
    expect(title.textContent).toContain(mockMessages[4].sender.firstName);
  });

  it('contains send button', () => {
    const sendButton =
      fixture.debugElement.nativeElement.querySelector('#send-button');
    expect(sendButton).toBeTruthy();

    sendButton.click();
    fixture.detectChanges();
  });

  it('contains input box for submitting messages', () => {
    const inputElement =
      fixture.debugElement.nativeElement.querySelector('#message-input');
    expect(inputElement).toBeTruthy();
  });
});
