import {
  AfterViewInit,
  Component,
  ElementRef,
  HostL<PERSON>ener,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Thread, Message, DeliveryStatus } from 'src/app/models';
import { ImageMessageData, Status } from 'src/app/models';
import { SnackbarService } from 'src/app/services/snackbar.service';
import { Subject, from, map, mergeMap, tap } from 'rxjs';
import { MessagingStateService } from '../../services/messaging-state.service';
import { BriAlertComponent, DialogData } from '@davita/bridge-library/alert';
import { DeviceStateService } from 'src/app/services/device-state.service';
import { MessagingProxyService } from 'src/app/services/messaging-proxy.service';
import { UserService } from 'src/app/services/user.service';
import { UserTypeService } from 'src/app/services/user-type.service';
import { UserType } from 'src/app/models/roles';
import { Location } from '@angular/common';

@Component({
  selector: 'app-thread-messages',
  templateUrl: './thread-messages.component.html',
  styleUrls: ['./thread-messages.component.scss'],
})
export class ThreadMessagesComponent implements AfterViewInit {
  @ViewChild(BriAlertComponent, { static: false })
  private component!: BriAlertComponent;
  @ViewChild('scrollContainer') scrollContainerRef: ElementRef = {
    nativeElement: {},
  };
  thread: Thread = {} as Thread;
  id: string = '';
  newMessageStatus: Status = Status.idle;
  newMessageFormControl: FormControl = new FormControl('', [
    Validators.maxLength(2054),
  ]);
  errorColor = '#D55027';
  triggerInfiniteScroll$ = new Subject<number>();
  showSpinner = false;
  readOnlyThread = false;
  Status = Status; // expose enum
  private spinnerStateUpdated = false;
  private postingOptimisticUpdate = false;
  private newPageOfMessagesPosted = false;
  private currentScrollHeight = 0;
  private prevoiusScrollPosition = 0;
  private observer!: MutationObserver;
  private readonly LOAD_NEW_MESSAGES_THRESHOLD = 25;
  deviceType: string = this.deviceState.determineDevice();
  public isTeammate = false;

  constructor(
    private route: ActivatedRoute,
    private proxyService: MessagingProxyService,
    private messageState: MessagingStateService,
    private snackbarService: SnackbarService,
    private renderer: Renderer2,
    private router: Router,
    public deviceState: DeviceStateService,
    private userService: UserService,
    private userTypeService: UserTypeService,
    private location: Location,
  ) {
    this.thread = this.route.snapshot.data['thread'];
    this.route.params.subscribe(({ id }) => {
      this.prevoiusScrollPosition = 0;
      this.newPageOfMessagesPosted = false;
      this.newMessageStatus = Status.idle;
      this.id = id;
    });
    this.route.data.subscribe(({ thread }) => {
      this.thread = thread;
      this.readOnlyThread = !!this.thread.noReply;
    });
    this.userTypeService.userType$
      .pipe(tap((user) => (this.isTeammate = user == UserType.TEAMMATE)))
      .subscribe();
  }

  ngAfterViewInit() {
    this.currentScrollHeight =
      this.scrollContainerRef.nativeElement.scrollHeight;

    // listen for changes in messaging viewport
    this.observer = new MutationObserver(() => {
      // if new page of messages was loaded the fix scroll position
      // otherwise scroll to bottom
      if (this.newPageOfMessagesPosted && !this.postingOptimisticUpdate) {
        this.newPageOfMessagesPosted = false;
        this.fixScrollPosition();
      } else if (this.spinnerStateUpdated && !this.postingOptimisticUpdate) {
        this.fixScrollPosition();
      } else if (
        this.newMessageStatus === Status.error ||
        this.newMessageStatus === Status.loading
      ) {
        this.scrollToBottom();
      }
    });
    this.observer.observe(this.scrollContainerRef.nativeElement, {
      childList: true, // observe direct children
      subtree: true, // and lower descendants too
    });

    // record initial scroll position to compare with current position
    // to see if user is scrolling up or down
    this.prevoiusScrollPosition =
      this.scrollContainerRef.nativeElement.scrollTop;

    // listen for changes in scroll position
    this.renderer.listen(
      this.scrollContainerRef.nativeElement,
      'scroll',
      (event) => {
        const currentScrollPosition = event.target.scrollTop;

        const scrollingUp = currentScrollPosition < this.prevoiusScrollPosition;

        if (
          scrollingUp &&
          currentScrollPosition <= this.LOAD_NEW_MESSAGES_THRESHOLD
        ) {
          this.triggerInfiniteScroll$.next(currentScrollPosition);
        }

        this.prevoiusScrollPosition = currentScrollPosition;
      },
    );
  }

  ngOnDestory() {
    this.observer.disconnect();
    this.triggerInfiniteScroll$.complete();
  }

  sendMessage(imagesData: ImageMessageData[] | void) {
    let imageFiles: File[] = [] as File[];
    let lastFileName: string = '';

    if (
      this.newMessageFormControl.errors &&
      this.newMessageFormControl.errors['maxlength']
    ) {
      this.snackbarService.openErrorToast(
        'Message is too long, please limit to 2054 characters.',
      );
      return;
    }

    // if there are images , prepare them
    if (imagesData && imagesData.length > 0) {
      // extract array of File blobs from imagesData
      lastFileName = imagesData[imagesData.length - 1].fileName;
      imageFiles = imagesData.map((image) => image.imageFile);
    }

    this.postingOptimisticUpdate = true;
    this.newMessageStatus = Status.loading;
    const messageId = this.messageState.addNewMessageOptimisticUpdate(
      this.newMessageFormControl.value,
      this.id,
      imageFiles,
    );

    this.proxyService
      .getHttpService()
      .postMessagePlusFileByThreadId(
        this.newMessageFormControl.value || lastFileName,
        this.id,
        imageFiles,
      )
      .subscribe({
        next: () => {
          this.snackbarService.openSuccessToast('Message Sent');
          this.newMessageFormControl.patchValue('', { emitEvent: false });
          // TODO: Need to update message with id from database to view images
          this.messageState.updateOptimisticMessageState(
            messageId,
            this.id,
            DeliveryStatus.delivered,
          );
          this.messageState.updateThreadAfterSendingNewMessage(
            messageId,
            this.id,
          );
          this.newMessageStatus = Status.success;
        },
        error: () => {
          this.messageState.updateOptimisticMessageState(
            messageId,
            this.id,
            DeliveryStatus.undelivered,
          );
          this.snackbarService.openErrorToast('Message Not Delivered');
          this.newMessageFormControl.patchValue('', { emitEvent: false });
          this.newMessageStatus = Status.error;
        },
        complete: () => {
          this.postingOptimisticUpdate = false;
        },
      });
  }

  // find all undelivered messages of
  // current thread and attempt to post them
  submitUndeliveredMessages(threadId: string) {
    const undeliveredMessages =
      this.messageState.getUndeliveredMessages(threadId);

    this.newMessageStatus = Status.loading;

    from(undeliveredMessages)
      .pipe(
        mergeMap((undeliveredMessage: Message) => {
          // check if this message has an image
          const imagefiles: File[] = [];
          if (undeliveredMessage.undeliveredImage?.file)
            imagefiles.push(undeliveredMessage.undeliveredImage.file);

          return this.proxyService
            .getHttpService()
            .postMessagePlusFileByThreadId(
              undeliveredMessage.messageText,
              threadId,
              imagefiles,
            )
            .pipe(map(() => undeliveredMessage.id));
        }),
        tap((id) => {
          // clean up , now that message are successfully posted
          this.messageState.updateOptimisticMessageState(
            id,
            threadId,
            DeliveryStatus.delivered,
          );
        }),
      )
      .subscribe({
        next: () => {
          this.newMessageStatus = Status.success;
          this.snackbarService.openSuccessToast('Message Sent');
        },
        error: () => {
          this.newMessageStatus = Status.error;
          this.snackbarService.openSuccessToast('Failed to Post Message');
        },
      });
  }

  // ************************************
  // Infinite Scroll and Scrolling Methods
  // ************************************
  scrollToBottom() {
    const element = this.scrollContainerRef.nativeElement;
    element.scrollTop = element.scrollHeight;
    this.newPageOfMessagesPosted = false;
  }

  fixScrollPosition() {
    // Calculate new scroll height after adding messages
    const newScrollHeight = this.scrollContainerRef.nativeElement.scrollHeight;

    // Adjust scroll position to maintain the same view
    this.scrollContainerRef.nativeElement.scrollTop =
      newScrollHeight - this.currentScrollHeight;

    // reset that now new page is loaded
    this.newPageOfMessagesPosted = false;
  }

  onNewPageLoaded(scrollHeight: number) {
    this.currentScrollHeight = scrollHeight;
    this.newPageOfMessagesPosted = true;
  }

  onNewPageLoading(showSpinner: boolean) {
    this.spinnerStateUpdated = true;
    this.showSpinner = showSpinner;
  }

  // ************************************
  // Modal Methods
  // ************************************
  openRetryModal() {
    const data: DialogData = {
      header: 'Message Not Delivered',
      description:
        'Your message was not sent. Tap "Try Again" to send this message.',
      primaryButtonLabel: 'Try Again',
      secondaryButtonLabel: 'Close',
    };
    this.component.open(data);
  }

  tryAgainButton() {
    this.submitUndeliveredMessages(this.id);
    this.component.close();
  }

  closeModal() {
    this.component.close();
  }

  closeMessage() {
    this.router.navigate(['../../landing'], {
      skipLocationChange: true,
      relativeTo: this.route,
    });
    this.deviceState.threadListVisible = true;
  }

  @HostListener('window:resize')
  onResize(): void {
    this.deviceType = this.deviceState.determineDevice();
  }

  public getSubject(): string {
    return `Subject: ${this.thread.subject}`;
  }

  public getTitle(): string {
    return this.isTeammate
      ? this.userService.getPatientName(this.thread)
      : this.thread.subject;
  }
}
