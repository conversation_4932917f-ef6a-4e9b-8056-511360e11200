import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router } from '@angular/router';
import {
  EMPTY,
  Observable,
  catchError,
  filter,
  first,
  iif,
  map,
  of,
  switchMap,
  tap,
} from 'rxjs';
import { Thread } from '../models';
import { MessagingStateService } from '../services/messaging-state.service';
import { MessagingProxyService } from '../services/messaging-proxy.service';
import { SnackbarService } from '../services/snackbar.service';

@Injectable({
  providedIn: 'root',
})
export class threadResolverService implements Resolve<Thread | undefined> {
  constructor(
    private proxyService: MessagingProxyService,
    private messageState: MessagingStateService,
    private router: Router,
    private snackbardService: SnackbarService,
  ) {}

  resolve(route: ActivatedRouteSnapshot) {
    const { id } = route.params;
    // get user external id to see if message from
    // from websocket is from current user
    return this.messageState.threads$.pipe(
      switchMap((threads) => {
        const threadId = threads.findIndex((thread) => thread.id == id);
        // If thread exists, return them wrapped in an Observable
        // Otherwise, fetch new messages for the thread ID
        return iif(
          () => threadId >= 0,
          of(threads[threadId]),
          this.fetchUserThreadbyId(id),
        );
      }),
      first(),
      catchError(() => {
        this.snackbardService.openErrorToast('Error, please try again');
        if (this.router.url.includes('teammate-messaging')) {
          this.router.navigate(['/teammate-messaging/landing'], {
            replaceUrl: true,
          });
        } else {
          this.router.navigate(['/messaging/landing'], { replaceUrl: true });
        }
        return EMPTY;
      }),
    );
  }

  fetchUserThreadbyId(id: string): Observable<Thread | undefined> {
    return this.proxyService
      .getHttpService()
      .getUserThreads()
      .pipe(
        tap((threads) => {
          this.messageState.updateThreads(threads);
        }),

        switchMap(() => this.messageState.threads$),
        // Convert the stream of ThreadDTO[] into a stream of ThreadDTO
        map((threads: Thread[]) => threads.find((thread) => thread.id === id)),

        // Filter out any undefined results (in case no match was found)
        filter((thread: Thread | undefined) => thread !== undefined),

        // Take the first matching ThreadDTO
        first(),
      );
  }
}
