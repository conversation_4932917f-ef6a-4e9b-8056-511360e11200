<div *ngIf="dataSource" class="teammate-inbox">
  <div class="inbox-table-container">
    <table mat-table [dataSource]="dataSource" [fixedLayout]="true">
      <ng-container
        *ngFor="let column of columnsData"
        [matColumnDef]="column.columnDef"
      >
        <th mat-header-cell *matHeaderCellDef class="mat-body-1">
          <div class="increase-weight">
            {{ column.header | titlecase }}
          </div>
        </th>
        <td mat-cell *matCellDef="let row" class="teammate-inbox-column">
          <div
            class="firstline inbox-cell-data"
            [ngClass]="{
              'mat-body-1': column.header === 'subject',
              'mat-body': column.header === 'activity',
              'mat-subtitle-1': column.header === 'patient',
            }"
          >
            {{ row[column.header][0] }}
          </div>
          <div
            class="inbox-cell-data"
            [ngClass]="column.header === 'subject' ? 'mat-caption' : 'mat-body'"
            *ngIf="row[column.header].length > 1 && row[column.header][1]"
          >
            <span *ngIf="column.header === 'patient'" class="overline"
              >MPI:
            </span>
            {{ row[column.header][1] }}
          </div>
        </td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="columnNames; sticky: true"></tr>
      <tr
        mat-row
        *matRowDef="let row; columns: columnNames"
        (click)="openThread(row)"
      ></tr>
    </table>
  </div>
  <mat-paginator
    [pageSize]="pageSize"
    class="mat-caption paginator"
    [showFirstLastButtons]="false"
    [hidePageSize]="true"
  >
  </mat-paginator>
</div>
