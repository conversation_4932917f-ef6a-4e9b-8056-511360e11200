@import "@davita/bridge-library/scss/colors";

.teammate-inbox {
  display: flex;
  flex-direction: column;
  height: 100%;
  .inbox-table-container {
    overflow: auto;
    flex: 1;
  }
  table {
    @media (prefers-color-scheme: dark) {
      background-color: $dark-color-ui-background !important;
    }
    background-color: $light-color-ui-background;
    width: 100%;
  }
  td {
    max-width: 0 !important;
    &.mat-column-patient {
      width: 25%;
    }
    &.mat-column-activity {
      width: 25%;
    }
    &.mat-column-subject {
      max-width: 50%;
      .inbox-cell-data {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .increase-weight {
    font-weight: 700;
  }
}
