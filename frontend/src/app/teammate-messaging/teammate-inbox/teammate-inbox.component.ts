import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { Thread } from 'src/app/models';
import { MessagingStateService } from 'src/app/services/messaging-state.service';
import { MessagingSenderUserType } from 'src/app/models/roles';
import {
  MatTableDataSource,
  MatTableDataSourcePaginator,
} from '@angular/material/table';
import { skipWhile, tap } from 'rxjs';
import { DatePipe } from '@angular/common';
import { MatPaginator } from '@angular/material/paginator';
import { ActivatedRoute, Router } from '@angular/router';
import { DeviceStateService } from 'src/app/services/device-state.service';

@Component({
  selector: 'app-teammate-inbox',
  templateUrl: './teammate-inbox.component.html',
  styleUrls: ['./teammate-inbox.component.scss'],
})
export class TeammateInboxComponent implements OnInit, AfterViewInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  public columnNames = ['patient', 'subject', 'activity'];
  public dataSource?: MatTableDataSource<any, MatTableDataSourcePaginator>;
  public columnsData?: any[];
  public pageSize = 50;

  constructor(
    private messageState: MessagingStateService,
    private datePipe: DatePipe,
    private router: Router,
    private route: ActivatedRoute,
    private deviceState: DeviceStateService,
  ) {}

  ngOnInit(): void {
    this.deviceState.inboxVisible = true;
    this.messageState.threads$
      .pipe(
        skipWhile((threads) => !threads),
        tap((threads) => {
          if (!this.dataSource) {
            this.dataSource = new MatTableDataSource<any>(
              this.generateTableData(threads),
            );
          } else {
            this.dataSource.data = this.generateTableData(threads);
          }
        }),
      )
      .subscribe();

    this.columnsData = this.columnNames.map((c) => {
      return {
        columnDef: c.toLowerCase(),
        header: c.toLowerCase(),
        wrapCss: true,
      };
    });
  }

  ngAfterViewInit(): void {
    if (this.dataSource) {
      this.dataSource.paginator = this.paginator;
    }
  }

  openThread(data: any) {
    this.router.navigate([`../thread/${data.id}`], {
      relativeTo: this.route,
      skipLocationChange: true,
    });
    this.deviceState.inboxVisible = false;
  }

  private generateTableData(threads: Thread[]) {
    const today = this.datePipe.transform(new Date(), 'M/d/y');
    return threads.map((thread) => {
      const patient = thread.participants.filter(
        (p) => p.userType === MessagingSenderUserType.patient,
      )[0];
      const receivedDate = this.datePipe.transform(
        thread.lastMessageSentAt,
        'M/d/y',
      );
      const receivedTime = this.datePipe.transform(
        thread.lastMessageSentAt,
        'shortTime',
      );
      const acitivty =
        today === receivedDate
          ? receivedTime
          : `${receivedDate} at ${receivedTime}`;
      return {
        id: thread.id,
        patient: [
          patient ? `${patient.firstName} ${patient.lastName}`.trim() : null,
          patient ? patient.mpi : null,
        ],
        subject: [thread.subject, thread.lastMessage],
        activity: [acitivty],
      };
    });
  }
}
