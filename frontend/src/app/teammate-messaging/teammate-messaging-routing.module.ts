import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { TeammateMessagingComponent } from './teammate-messaging.component';
import { NewMessageComponent } from '../shared/new-message/new-message.component';
import { ThreadMessagesComponent } from '../shared/thread-messages/thread-messages.component';
import { threadResolverService } from '../shared/thread.resolver';
import { BriTeammateCanAccessGuard } from '@davita/bridge-utility-library/authorization';
import { TeammateInboxComponent } from './teammate-inbox/teammate-inbox.component';

const routes: Routes = [
  {
    path: '',
    component: TeammateMessagingComponent,
    canActivate: [BriTeammateCanAccessGuard],
    children: [
      {
        path: '',
        redirectTo: 'landing',
        pathMatch: 'full',
      },
      {
        path: 'landing',
        component: TeammateInboxComponent,
      },
      {
        path: 'new',
        component: NewMessageComponent,
      },
      {
        path: 'thread/:id',
        component: ThreadMessagesComponent,
        resolve: { thread: threadResolverService },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TeammateMessagingRoutingModule {}
