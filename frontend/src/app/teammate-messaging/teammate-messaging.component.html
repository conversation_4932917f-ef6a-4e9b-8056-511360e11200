<div
  [ngClass]="{ 'teammate-messaging-body-mobile': deviceType !== 'desktop' }"
  class="teammate-messaging-body"
>
  <div
    id="teammate-messaging-main-content"
    class="main-content"
    [ngClass]="
      deviceState.threadListVisible ? 'legal-padding' : 'footer-padding'
    "
  >
    <div [ngClass]="{ 'sub-header-container': deviceType === 'desktop' }">
      <bri-sub-header-v2
        *ngIf="!onLandingPage"
        [sectionHeader]="'Messages'"
        [leftContent]="{ showBackButton: true, backText: 'BACK' }"
        (backBtnClicked)="goBack()"
        [hideBackIcon]="false"
        [placeholder]="''"
      />
      <bri-sub-header-v2
        *ngIf="onLandingPage"
        class="header-extra-spacing"
        [leftContent]="{ showBackButton: true, backText: '' }"
        [sectionHeader]="'Messages'"
        (backBtnClicked)="goBack()"
        [hideBackIcon]="false"
        [placeholder]="''"
        [rightContent]="
          deviceType !== 'desktop' &&
          messageLoadStatus === Status.success &&
          onLandingPage
            ? [{ name: 'di di-document44', text: 'New Message' }]
            : undefined
        "
        (rightButtonClicked)="newMessage()"
      />
      <div
        class="new-message-button"
        *ngIf="
          deviceType === 'desktop' &&
          messageLoadStatus === Status.success &&
          onLandingPage
        "
      >
        <bri-buttons
          briFlat
          [btnText]="'New Message'"
          [iconName]="'di di-document44'"
          (buttonClicked)="newMessage()"
        />
      </div>
    </div>
    <ng-container *ngIf="messageLoadStatus === Status.success; else loadingApp">
      <div class="content">
        <!-- turned off, could use !deviceState.inboxVisible to only hide in inbox view -->
        <div class="local-message-list" *ngIf="false">
          <div class="threadsContainer">
            <div class="scroll-container" #scrollContainer>
              <ng-container *ngIf="threadsLoaded; else loadingThreads">
                <ng-container *ngFor="let thread of threads">
                  <div
                    id="thread-element"
                    (click)="threadClick(thread)"
                    [ngClass]="
                      thread.id === this.activeThread
                        ? 'selectedThread'
                        : 'threadStyle'
                    "
                  >
                    <bri-master-list-item
                      class="thread-message-list-item"
                      [id]="thread.id"
                      type="icon"
                      [textType]="'line3'"
                      [textList]="[
                        {
                          name: 'line1',
                          text: getPatientName(thread),
                          className: 'overline',
                        },
                        {
                          name: 'line2',
                          text: 'Subject: ' + thread.subject,
                          className: 'mat-caption',
                        },
                        {
                          name: 'line3',
                          text: thread.lastMessage,
                          className: 'mat-caption mat-caption-gray',
                        },
                      ]"
                      [rightText]="dateFormat(thread.lastMessageSentAt)"
                      [badge]="thread.unreadMessages > 0"
                      [badgeNumber]="thread.unreadMessages"
                      [overlapOff]="true"
                    />
                  </div>
                  <mat-divider />
                </ng-container>
              </ng-container>
              <ng-template #loadingThreads>
                <div class="loading">
                  <bri-progress-bar
                    message="Getting Messages"
                  ></bri-progress-bar>
                </div>
              </ng-template>
            </div>
          </div>
          <div class="legal-text">
            <mat-divider />
            <p class="mat-caption">
              In case of emergency please dial 911. If you need an immediate
              response, please contact the clinician directly.
            </p>
          </div>
        </div>
        <mat-divider
          *ngIf="deviceType === 'desktop' && threads.length > 0"
          class="divider-shadow"
        />
        <div
          [ngClass]="{
            'chat-space': deviceType === 'desktop' && threads.length > 0,
            'chat-space-singular':
              deviceType === 'desktop' && threads.length <= 0,
            'chat-space-mobile': deviceType !== 'desktop',
          }"
        >
          <router-outlet></router-outlet>
        </div>
      </div>
    </ng-container>
    <ng-template #loadingApp>
      <ng-container
        *ngIf="messageLoadStatus === Status.loading; else ErrorLoading"
      >
        <div class="loading">
          <bri-progress-bar [message]="loadingMessage"></bri-progress-bar>
        </div>
      </ng-container>
      <ng-template #ErrorLoading>
        <div class="error">
          <bri-empty-state
            [isCircleBorder]="false"
            [isEmptyStateSVG]="false"
            [iconClass]="'di di-alert'"
            [headerText]="'Error'"
            [messageText]="errorDescription"
          ></bri-empty-state>
        </div>
      </ng-template>
    </ng-template>
  </div>
</div>
