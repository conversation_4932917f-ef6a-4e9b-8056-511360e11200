@import "@davita/bridge-library/scss/colors";

.teammate-messaging-body {
  width: 100%;
  height: calc(100dvh - 96px);
  min-height: unset !important;
  overflow: hidden;
  background-color: $light-color-ui-grey-background;
  &.teammate-messaging-body-mobile {
    position: fixed;
    .main-content {
      padding-top: 48px !important;
      &.legal-padding {
        padding-bottom: calc(
          54px + env(safe-area-inset-bottom, 0) + env(safe-area-inset-top)
        ) !important;
      }
      &.footer-padding {
        padding-bottom: calc(
          54px + 32px + env(safe-area-inset-bottom, 0) +
            env(safe-area-inset-top)
        ) !important;
      }
    }
  }
  @media (prefers-color-scheme: dark) {
    background-color: $dark-color-ui-grey-background;
  }
}

.sub-header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  align-self: stretch;
  padding: 8px 32px 0;
  margin: 32px 0 16px;
  h1 {
    color: $light-color-text-100;
    @media (prefers-color-scheme: dark) {
      color: $dark-color-text-100;
    }
  }
}

.error {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.error bri-empty-state ::ng-deep .empty-state-container {
  background-color: unset !important;
}

.error bri-empty-state ::ng-deep .empty-state-icon {
  color: $light-color-blue;
  @media (prefers-color-scheme: dark) {
    color: $dark-color-text-75;
    color: $dark-color-light-blue;
  }
}

.new-message-button {
  align-self: self-end;
  padding-bottom: 16px;
  bri-buttons {
    display: flex;
    height: 48px;
    justify-content: center;
    align-items: center;
    gap: 2px;
  }
}

.main-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.content {
  padding: 0 32px;
  display: flex;
  flex-direction: row;
  width: 100%;
  flex-grow: 1;
  overflow: auto;
}

.local-message-list {
  display: flex;
  flex-direction: column;
  width: 400px;
  min-width: 360px;
  background-color: $light-color-ui-background;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  box-shadow:
    0px 0px 1px rgba(0, 0, 0, 0.2),
    -1px 2px 2px rgba(0, 0, 0, 0.2);
  @media (prefers-color-scheme: dark) {
    background-color: $dark-color-ui-background;
  }
  mat-divider {
    margin: 0 16px;
  }
  bri-master-list-item {
    .mat-subtitle-1 {
      font-size: 18px;
    }
    .mat-caption {
      font-size: 16px;
    }
    ::ng-deep bri-badge {
      width: 20px !important;
      div.badge > span {
        min-width: 20px;
        border-radius: 20px;
        text-align: center;
      }
    }
  }
  .scroll-container {
    overflow-y: auto;
    height: 100%;
  }
}

.chat-space {
  background-color: $light-color-ui-background;
  flex-grow: 1;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  box-shadow:
    0px 0px 1px rgba(0, 0, 0, 0.2),
    1px 2px 2px rgba(0, 0, 0, 0.2);
  @media (prefers-color-scheme: dark) {
    background-color: $dark-color-ui-background;
  }
}
.chat-space-singular {
  background-color: $light-color-ui-background;
  flex-grow: 1;
  border-radius: 4px;
  box-shadow:
    0px 0px 1px rgba(0, 0, 0, 0.2),
    1px 2px 2px rgba(0, 0, 0, 0.2),
    -1px 2px 2px rgba(0, 0, 0, 0.2);
  @media (prefers-color-scheme: dark) {
    background-color: $dark-color-ui-background;
  }
}
.chat-space-mobile {
  background-color: $light-color-ui-background;
  flex-grow: 1;
  @media (prefers-color-scheme: dark) {
    background-color: $dark-color-ui-background;
  }
}
.divider-shadow {
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.2);
}
.loading {
  display: flex;
  justify-content: center; /* Aligns horizontally */
  align-items: center; /* Aligns vertically */
  margin: auto;
}
.selectedThread {
  background-color: rgba(194, 222, 224, 0.25);
  min-height: 70px;
}
.threadStyle {
  min-height: 70px;
}
.selectedThread:hover,
.selectedThread:active,
.threadStyle:hover,
.threadStyle:active {
  background-color: $light-color-ui-grey-background;
  @media (prefers-color-scheme: dark) {
    background-color: $dark-color-ui-grey-background;
  }
}
bri-sub-header-v2 ::ng-deep div.header-container {
  border-bottom: 1px solid $light-color-ui-disabled;
  @media (prefers-color-scheme: dark) {
    border-bottom: 1px solid $dark-color-ui-disabled;
  }
}
bri-master-list-item ::ng-deep .line2 {
  color: $light-color-text-75;
  @media (prefers-color-scheme: dark) {
    color: $dark-color-text-75;
  }
}
bri-master-list-item ::ng-deep .mat-body-2 {
  color: $light-color-text-50;
  @media (prefers-color-scheme: dark) {
    color: $dark-color-text-50;
  }
}

bri-master-list-item.thread-message-list-item ::ng-deep .right-text1 {
  display: flex !important;
  flex-direction: column;
  margin-top: 0px;
  align-items: flex-end;
  height: 48px;
  justify-content: center;
}

mat-divider {
  background-color: $light-color-ui-separator;
  border: 1px solid $light-color-ui-separator;
  @media (prefers-color-scheme: dark) {
    background-color: $dark-color-ui-separator;
    border: 1px solid $dark-color-ui-separator;
  }
}

.threadsContainer {
  flex-grow: 1;
  overflow-y: auto;
}

.threadsContainer::-webkit-scrollbar {
  width: 0px;
}
.legal-text {
  padding: 0 8px 16px;
  background-color: $light-color-ui-background;
  border-bottom-left-radius: 4px;
  @media (prefers-color-scheme: dark) {
    background-color: $dark-color-ui-background;
  }
  p {
    padding-top: 8px;
    text-align: center;
    color: $light-color-text-25;
    background-color: $light-color-ui-background;
    @media (prefers-color-scheme: dark) {
      color: $dark-color-text-25;
      background-color: $dark-color-ui-background;
    }
  }
}

@media screen and (max-width: 959px) {
  body {
    padding-top: 48px;
  }
  .content {
    padding: 0;
    margin-bottom: 0;
  }
  .local-message-list {
    width: 100%;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .threadsContainer {
    height: calc(100% - 48px);
  }

  .teammate-messaging-body {
    bri-sub-header-v2 {
      ::ng-deep .header-container {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        z-index: 999;
        margin-top: env(safe-area-inset-top, 0);
      }
    }
  }
}

@media screen and (max-width: 824px) {
  .threadsContainer {
    height: calc(100% - 72px);
  }
}

@media screen and (max-width: 599px) {
  .content {
    padding: 0;
  }
}

@media screen and (max-width: 435px) {
  .threadsContainer {
    height: calc(100% - 94px);
  }
}

@media screen and (max-width: 291px) {
  .threadsContainer {
    height: calc(100% - 120px);
  }
}

.teammate-inbox {
  flex: 0 0 100%;
}

.header-extra-spacing {
  padding-top: 48px;
}
