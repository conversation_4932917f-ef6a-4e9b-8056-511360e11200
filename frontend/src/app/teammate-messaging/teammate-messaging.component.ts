import { Component, HostListener, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { BriBadgeType } from '@davita/bridge-library/shared';
import { MessagingUser, Status } from 'src/app/models';
import { Thread } from '../models/index';
import { DatePipe, Location } from '@angular/common';
import { WebSocketService } from '../services/web-socket.service';
import { MessagingStateService } from '../services/messaging-state.service';
import {
  Subscription,
  combineLatest,
  filter,
  skipWhile,
  switchMap,
  tap,
} from 'rxjs';
import { DeviceStateService } from '../services/device-state.service';
import { UserService } from '../services/user.service';
import { Capacitor } from '@capacitor/core';
import { UserTypeService } from '../services/user-type.service';
import { BriTeammateAuthorizationService } from '@davita/bridge-utility-library/authorization';
import { UserType } from '../models/roles';
import { HttpTeammateMessagingService } from '../services/http-teammate-messaging.service';
import { MessagingProxyService } from '../services/messaging-proxy.service';

@Component({
  selector: 'app-teammate-messaging',
  templateUrl: './teammate-messaging.component.html',
  styleUrls: ['./teammate-messaging.component.scss'],
  providers: [DatePipe],
})
export class TeammateMessagingComponent implements OnInit, OnDestroy {
  threads: Thread[] = [] as Thread[];
  activeThread = '';
  messageLoadStatus: Status = Status.idle;
  threadsLoaded = false; // thread loading state
  private routerSubscription!: Subscription;
  private messagingSubscription!: Subscription;
  deviceType: string = '';
  Status = Status;
  errorDescription: string =
    "We're currently down for maintenance. Please check back soon.";
  onLandingPage = true;
  loadingMessage = 'Select a Facility';

  constructor(
    private router: Router,
    private datePipe: DatePipe,
    private location: Location,
    private webSocketService: WebSocketService,
    private httpMessagingService: HttpTeammateMessagingService,
    private messageState: MessagingStateService,
    public deviceState: DeviceStateService,
    private userService: UserService,
    private userTypeService: UserTypeService,
    private briAuthService: BriTeammateAuthorizationService,
    private proxyService: MessagingProxyService,
    private route: ActivatedRoute,
  ) {
    this.onLandingPage = this.router.url.includes('landing');
    this.proxyService.setupHttpService();
    this.userTypeService.setUserType(UserType.TEAMMATE);
    const teammate$ = this.briAuthService.user$.pipe(
      skipWhile((user) => !user),
      tap((user) => {
        this.httpMessagingService.teammateUser = user?.oim_details;
        const messageUser = {
          adUsername: user?.oim_details.userLogin,
        } as MessagingUser;
        this.messageState.setUser(messageUser);
        // initial websocket connection
        if (!Capacitor.isNativePlatform()) {
          this.webSocketService.initWebSocket(user?.oim_details.userLogin);
        }
      }),
    );

    const facility$ = this.briAuthService.facility$.pipe(
      skipWhile((facility) => facility == null),
      tap((facility) => {
        this.loadingMessage = 'Getting Messages';
        this.httpMessagingService.teammateFacility = facility;
        this.router.navigate(['./landing'], {
          skipLocationChange: true,
          relativeTo: this.route,
        });
      }),
    );

    combineLatest([teammate$, facility$]).subscribe(() => {
      this.messagingSubscription = this.userService
        .createUserIfNotExists()
        .pipe(
          filter((user) => user && Object.keys(user).length > 0),
          switchMap(() => this.httpMessagingService.getUserThreads()),
        )
        .subscribe({
          next: (threads: Thread[]) => {
            this.messageState.updateThreads(threads);
            this.messageLoadStatus = Status.success;
          },
          error: (err) => {
            console.error(err);
            this.messageLoadStatus = Status.error;
          },
        });
    });
    // make API call to endpoint and grab all Threads
    // threads are saved to messagingState
    this.messageLoadStatus = Status.loading;

    this.router.events.subscribe({
      next: (event) => {
        if (event instanceof NavigationEnd) {
          this.onLandingPage = this.router.url.includes('landing');
        }
      },
    });
  }

  ngOnInit() {
    this.deviceType = this.deviceState.determineDevice();
    this.routerSubscription = this.messageState.threads$
      .pipe(
        switchMap((threads) => {
          this.threads = threads;
          this.threadsLoaded = true;

          return this.router.events.pipe(
            filter(
              (event): event is NavigationEnd => event instanceof NavigationEnd,
            ),
          );
        }),
      )
      .subscribe((event: NavigationEnd) => {
        // detect current thread and set as active thread
        const threadId = event.url.split('/').pop() as string;
        this.activeThread = threadId;

        // set unreadMessage on current thread to 0
        const currentThreadIndex = this.threads.findIndex(
          (thread) => thread.id === threadId,
        );
        if (currentThreadIndex !== -1)
          this.threads[currentThreadIndex].unreadMessages = 0;
      });

    // Check if the router has already navigated
    if (this.router.url) {
      const threadId = this.router.url.split('/').pop() as string;
      this.activeThread = threadId;
    }
  }

  // clean up
  ngOnDestroy() {
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
    if (this.messagingSubscription) {
      this.messagingSubscription.unsubscribe();
    }
  }

  dateFormat(dateString: string): string {
    const date = new Date(dateString);
    const normalizedDate = this.normalizeDate(date);
    const today = new Date();
    const normalizedToday = this.normalizeDate(today);

    // check if date is today , if it is show time
    if (normalizedDate.getTime() === normalizedToday.getTime())
      return this.datePipe.transform(date, 'h:mm a') || '';
    else return this.datePipe.transform(date, 'MM/dd') || '';
  }

  // set time to 12am so we are only comparing the day
  normalizeDate(date: Date) {
    const _date = new Date(date.getTime());
    _date.setHours(0, 0, 0, 0);
    return _date;
  }

  public briBadgeType = BriBadgeType;

  newMessage() {
    this.deviceState.inboxVisible = false;
    this.deviceState.threadListVisible = false;
    this.router.navigate(['./new'], {
      skipLocationChange: true,
      relativeTo: this.route,
    }); //use for IKC
  }

  threadClick(thread: Thread) {
    if (thread.unreadMessages) {
      // set unread messages to zero on backend
      this.httpMessagingService
        .updateThreadUnreadMessages(thread.id)
        .subscribe(() => {
          this.messageState.updateThreadUnreadMessageCount(thread.id, 0);
        });
    }
    this.deviceState.threadListVisible = false;
    this.router.navigate([`./thread/${thread.id}`], {
      skipLocationChange: true,
      relativeTo: this.route,
    }); //use for IKC
  }

  goBack() {
    if (this.router.url.includes('landing')) {
      this.location.back();
    } else {
      this.router.navigate(['./landing'], {
        skipLocationChange: true,
        relativeTo: this.route,
      });
    }
  }

  @HostListener('window:resize')
  onResize(): void {
    this.deviceType = this.deviceState.determineDevice();
  }

  public getPatientName(thread: Thread): string {
    return this.userService.getPatientName(thread);
  }
}
