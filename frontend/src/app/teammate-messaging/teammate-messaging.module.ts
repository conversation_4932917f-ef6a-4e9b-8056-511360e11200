import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TeammateMessagingComponent } from './teammate-messaging.component';
import { SharedModule } from '../shared/shared.module';
import { TeammateMessagingRoutingModule } from './teammate-messaging-routing.module';
import { TeammateInboxComponent } from './teammate-inbox/teammate-inbox.component';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';

@NgModule({
  declarations: [TeammateMessagingComponent, TeammateInboxComponent],
  imports: [
    CommonModule,
    TeammateMessagingRoutingModule,
    SharedModule,
    MatTableModule,
    MatPaginatorModule,
  ],
})
export class TeammateMessagingModule {}
